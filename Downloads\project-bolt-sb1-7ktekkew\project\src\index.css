@tailwind base;
@tailwind components;
@tailwind utilities;

/* Typography Base Styles */
@layer base {
  /* Fix horizontal overflow issues and smooth scrolling */
  html {
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "pnum" 1, "tnum" 0,
      "onum" 1, "lnum" 0, "dlig" 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    overflow-x: hidden;
    width: 100%;
    scroll-behavior: smooth;
    scroll-padding-top: 120px; /* Account for fixed navigation */
  }

  body {
    @apply font-body text-body text-charcoal;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
    overflow-x: hidden;
    width: 100%;
    position: relative;
  }

  /* Prevent horizontal scroll on all elements */
  * {
    box-sizing: border-box;
  }

  /* Root container fixes */
  #root {
    overflow-x: hidden;
    width: 100%;
    min-height: 100vh;
  }

  /* Heading defaults */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "dlig" 1;
  }

  /* Improved currency symbol rendering */
  .currency {
    font-feature-settings: "kern" 1, "liga" 1, "tnum" 1, "lnum" 1;
    font-variant-numeric: tabular-nums;
  }

  /* Better button text */
  button {
    @apply font-accent;
    font-feature-settings: "kern" 1, "liga" 1;
  }

  /* Navigation text optimization */
  nav {
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  }
}

/* Component-specific typography */
@layer components {
  .text-luxury {
    @apply font-heading font-semibold tracking-tight text-primary-800;
    font-feature-settings: "kern" 1, "liga" 1, "dlig" 1;
    background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 1px 2px rgba(249, 115, 22, 0.1);
  }

  .text-premium {
    @apply font-accent font-medium text-secondary-600;
    font-feature-settings: "kern" 1, "liga" 1;
  }

  .text-price {
    @apply font-accent font-bold currency text-secondary-700;
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-location {
    @apply font-body font-medium text-charcoal-700;
    font-feature-settings: "kern" 1, "liga" 1;
  }

  /* Luxury Card Styles */
  .card-luxury {
    @apply bg-gradient-to-br from-accent-50 to-accent-100 shadow-card-premium border border-accent-200/50;
    backdrop-filter: blur(10px);
  }

  .card-luxury:hover {
    @apply shadow-luxury-lg;
    transform: translateY(-2px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Premium Button Styles */
  .btn-luxury {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 text-white shadow-btn;
    @apply hover:shadow-btn-hover hover:from-primary-500 hover:to-primary-600;
    @apply transition-all duration-300 ease-out;
    @apply border border-primary-500/20;
  }

  .btn-luxury:hover {
    transform: translateY(-1px);
  }

  .btn-secondary-luxury {
    @apply bg-gradient-to-r from-secondary-600 to-secondary-700 text-white shadow-btn-gold;
    @apply hover:shadow-btn-gold-hover hover:from-secondary-500 hover:to-secondary-600;
    @apply transition-all duration-300 ease-out;
    @apply border border-secondary-500/30;
  }

  .btn-secondary-luxury:hover {
    transform: translateY(-1px);
  }

  .btn-outline-luxury {
    @apply border-2 border-primary-600 text-primary-600 bg-transparent;
    @apply hover:bg-primary-600 hover:text-white;
    @apply transition-all duration-300 ease-out;
    @apply shadow-card;
  }

  .btn-outline-luxury:hover {
    @apply shadow-btn;
    transform: translateY(-1px);
  }

  /* Glass Effect Components */
  .glass-effect {
    @apply bg-glass backdrop-blur-sm border border-accent-200/30 shadow-glass;
  }

  .glass-dark {
    @apply bg-glass-dark backdrop-blur-sm border border-primary-700/30 shadow-glass-dark;
  }

  /* Section Backgrounds */
  .section-luxury {
    @apply bg-gradient-to-br from-accent-50 via-accent-100 to-accent-50;
    background-image: radial-gradient(
        circle at 25% 25%,
        rgba(212, 175, 55, 0.05) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 75% 75%,
        rgba(30, 41, 59, 0.03) 0%,
        transparent 50%
      );
  }

  .section-premium {
    @apply bg-gradient-to-br from-primary-900 via-primary-800 to-primary-900;
    background-image: radial-gradient(
        circle at 20% 80%,
        rgba(212, 175, 55, 0.1) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 20%,
        rgba(212, 175, 55, 0.05) 0%,
        transparent 50%
      );
  }

  /* Luxury Dividers */
  .divider-luxury {
    @apply h-px bg-gradient-to-r from-transparent via-secondary-400 to-transparent;
  }

  .divider-premium {
    @apply h-0.5 bg-gradient-to-r from-transparent via-secondary-500 to-transparent;
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
  }

  /* Mobile-specific utilities */
  .mobile-container {
    @apply px-3 xs:px-4 sm:px-6 lg:px-8;
  }

  .mobile-padding {
    @apply p-3 xs:p-4 sm:p-6 lg:p-8;
  }

  .mobile-spacing {
    @apply space-y-3 xs:space-y-4 sm:space-y-6 lg:space-y-8;
  }

  /* Touch-friendly buttons */
  .btn-touch {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  /* Mobile overflow prevention utilities */
  .no-overflow {
    overflow-x: hidden;
    width: 100%;
    max-width: 100vw;
  }

  .mobile-safe {
    max-width: 100vw;
    overflow-x: hidden;
    position: relative;
  }

  /* Ensure all containers respect viewport width */
  .container-safe {
    max-width: 100%;
    overflow-x: hidden;
  }

  /* Mobile typography */
  .text-mobile-hero {
    @apply text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl;
  }

  .text-mobile-heading {
    @apply text-lg xs:text-xl sm:text-2xl md:text-3xl;
  }

  .text-mobile-body {
    @apply text-sm xs:text-base sm:text-lg;
  }

  /* Mobile grid utilities */
  .grid-mobile-2 {
    @apply grid grid-cols-1 xs:grid-cols-2;
  }

  .grid-mobile-3 {
    @apply grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-3;
  }

  .grid-mobile-4 {
    @apply grid grid-cols-2 lg:grid-cols-4;
  }

  /* Mobile flex utilities */
  .flex-mobile-col {
    @apply flex flex-col xs:flex-row;
  }

  /* Mobile form utilities */
  .form-mobile {
    @apply space-y-4 xs:space-y-6;
  }

  .input-mobile {
    @apply w-full px-3 xs:px-4 py-3 text-base min-h-[44px];
  }

  /* Mobile performance utilities */
  .transform-gpu {
    transform: translateZ(0);
  }

  /* Mobile scroll utilities */
  .scroll-mobile-smooth {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Enhanced mobile navigation */
  .mobile-nav-fix {
    scroll-margin-top: 120px;
  }

  /* Ensure sections have proper scroll offset */
  section {
    scroll-margin-top: 120px;
  }

  /* Enhanced microinteractions */
  .btn-microinteraction {
    @apply transition-all duration-300 ease-out;
    transform: translateZ(0);
  }

  .btn-microinteraction:hover {
    transform: translateY(-2px) translateZ(0);
  }

  .btn-microinteraction:active {
    transform: translateY(0) translateZ(0);
  }

  /* Card hover effects */
  .card-hover-lift {
    @apply transition-all duration-500 ease-out;
    transform: translateZ(0);
  }

  .card-hover-lift:hover {
    transform: translateY(-8px) translateZ(0);
  }

  /* Focus states for accessibility */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .focus-ring-inset {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset;
  }

  /* Smooth animations */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out forwards;
  }

  /* Loading states */
  .skeleton {
    @apply bg-gradient-to-r from-slate-200 via-slate-300 to-slate-200;
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Additional mobile fixes for white gap prevention */
  @media (max-width: 768px) {
    body {
      min-width: 100vw;
      max-width: 100vw;
    }

    * {
      max-width: 100vw;
    }

    /* Prevent any element from extending beyond viewport */
    .section, section, div, main, article, aside, nav, header, footer {
      max-width: 100vw;
      overflow-x: hidden;
    }

    /* Fix for common culprits */
    img, video, iframe, canvas, svg {
      max-width: 100%;
      height: auto;
    }

    /* Ensure grid and flex containers don't overflow */
    .grid, .flex, [class*="grid-"], [class*="flex-"] {
      max-width: 100vw;
      overflow-x: hidden;
    }
  }
}
