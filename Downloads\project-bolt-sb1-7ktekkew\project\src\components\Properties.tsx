import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";

// --- MOCK DATA ---
// This data is created to make the component functional.
// You would typically fetch this from a CMS or API.
const properties = [
  {
    id: 1,
    title: "Shreyas Sunrise",
    description: "A 30-acre premium plotted development strategically located near major employment hubs, offering the perfect blend of tranquility and connectivity. Your gateway to a lifestyle of luxury and convenience.",
    price: 12000000, // 1.2 Cr
    totalAcres: 30,
    status: "pre-launch",
    phases: [
      { name: "Phase 1", status: "Sold Out" },
      { name: "Phase 2", status: "Available" },
    ],
    images: [
      "https://images.pexels.com/photos/259588/pexels-photo-259588.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2",
      "https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2",
      "https://images.pexels.com/photos/221540/pexels-photo-221540.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2",
      "https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2",
    ],
    features: [
      "Gated Community with 24/7 Security",
      "Landscaped Parks and Open Spaces",
      "Modern Clubhouse with Swimming Pool",
      "Children's Play Area",
      "Underground Electrical Cabling",
      "Wide Asphalted Roads",
    ],
  },
];


// --- ANIMATION VARIANTS ---
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.15 },
  },
};

const itemVariants = {
  hidden: { y: 50, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { duration: 0.8, ease: "easeOut" },
  },
};


// --- PROPERTIES COMPONENT ---
const DevanahalliDevelopment = () => {
  const { ref, inView } = useInView({ triggerOnce: true, threshold: 0.1 });

  return (
    <section id="properties" className="py-16 sm:py-10 bg-primary-75 font-sans">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-12"
        >
          <motion.div variants={itemVariants} className="text-center space-y-4">
            <h2 className="text-4xl md:text-5xl font-bold text-black font-sans">
              Devanahalli Premium Plotted Development
            </h2>
            <div className="w-32 sm:w-40 md:w-48 h-1 bg-orange-500 mx-auto rounded-full" />
            <p className="text-lg text-slate-600 max-w-4xl mx-auto leading-relaxed">
              Experience North Bengaluru's most promising investment opportunity - a 30-acre premium plotted development strategically located near major employment hubs.
            </p>
          </motion.div>

          <motion.div variants={itemVariants}>
            {properties.map((property) => (
              <div key={property.id} className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-start bg-white rounded-3xl p-6 lg:p-8 shadow-lg">
                {/* Images Section */}
                <motion.div
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  className="flex flex-col space-y-4"
                >
                  <div className="relative overflow-hidden rounded-2xl shadow-lg group">
                    <img src={property.images[0]} alt={property.title} className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-700 min-h-[300px]" />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent" />
                    <div className="absolute top-4 left-4 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full">
                      <span className="text-sm font-bold text-orange-600">Pre-Launch Offer</span>
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    {property.images.slice(1, 4).map((image, imgIndex) => (
                      <div key={imgIndex} className="relative overflow-hidden rounded-xl shadow-md group">
                        <img src={image} alt={`${property.title} ${imgIndex + 2}`} className="w-full h-24 object-cover group-hover:scale-105 transition-transform duration-500" />
                      </div>
                    ))}
                  </div>
                </motion.div>

                {/* Content Section */}
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="flex flex-col space-y-6"
                >
                  <div className="space-y-3">
                    <h3 className="text-3xl md:text-4xl font-bold text-black font-sans">{property.title}</h3>
                    <div className="w-24 sm:w-28 md:w-32 h-1 bg-orange-500 rounded-full" />
                    <p className="text-slate-600 text-base leading-relaxed">{property.description}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-orange-50 border border-orange-200 rounded-xl">
                      <div className="text-2xl font-bold text-orange-600">₹{(property.price / 100000).toFixed(0)} L+</div>
                      <div className="text-sm text-slate-500 font-medium">Starting Price</div>
                    </div>
                    <div className="text-center p-4 bg-orange-50 border border-orange-200 rounded-xl">
                      <div className="text-2xl font-bold text-orange-600">{property.totalAcres}</div>
                      <div className="text-sm text-slate-500 font-medium">Total Acres</div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-xl font-bold text-slate-700 mb-3">Key Features</h4>
                    <div className="space-y-2">
                      {property.features.slice(0, 6).map((feature, i) => (
                        <div key={i} className="flex items-center space-x-3">
                          <div className="w-2 h-2 bg-orange-500 rounded-full flex-shrink-0"></div>
                          <span className="text-slate-600 font-medium text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4 pt-4">
                    <motion.button className="flex-1 bg-gradient-to-r from-orange-500 to-red-500 text-white px-6 py-3.5 rounded-xl font-bold shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300" whileTap={{ scale: 0.98 }}>
                      Schedule Site Visit
                    </motion.button>
                    <motion.button className="flex-1 bg-white text-orange-600 border-2 border-orange-500 px-6 py-3.5 rounded-xl font-bold hover:bg-orange-50 hover:scale-105 transition-all duration-300" whileTap={{ scale: 0.98 }}>
                      Download Brochure
                    </motion.button>
                  </div>
                </motion.div>
              </div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default DevanahalliDevelopment;
