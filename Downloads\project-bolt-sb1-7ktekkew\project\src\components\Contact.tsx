import React, { useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  Phone,
  Mail,
  MapPin,
  Send,
  User,
  MessageSquare,
  CheckCircle,
  MessageCircle,
} from "lucide-react";

// --- ANIMATION VARIANTS (Unchanged) ---
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.2, when: "beforeChildren" },
  },
};

const itemVariants = {
  hidden: { y: 30, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { duration: 0.6, ease: "easeOut" },
  },
};

// --- CONTACT COMPONENT (Redesigned) ---
const Contact = () => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    message: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);
      // Reset form after submission
      setFormData({ name: "", email: "", phone: "", message: "" });
    }, 1500);
  };

  const contactInfo = [
    {
      icon: Phone,
      title: "Call Us",
      details: ["+91-9036699799"],
      href: "tel:+919036699799",
    },
    {
      icon: Mail,
      title: "Email Us",
      details: ["<EMAIL>"],
      href: "mailto:<EMAIL>",
    },
    {
      icon: MapPin,
      title: "Our Location",
      details: ["Kalyan Nagar, Bengaluru, 560043"],
      href: "https://maps.google.com/?q=13.0219,77.6412", // Example coordinates for Kalyan Nagar
    },
  ];

  // --- MODERN "THANK YOU" SCREEN ---
  if (isSubmitted) {
    return (
      <section
        id="contact"
        className="py-20 bg-slate-50 flex items-center justify-center font-sans"
        style={{ minHeight: "80vh" }}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, type: "spring" }}
          className="text-center bg-white p-10 rounded-2xl shadow-lg max-w-lg mx-auto"
        >
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
          <h2 className="text-3xl font-bold text-gray-800 mt-6">Thank You!</h2>
          <p className="text-gray-600 mt-3">
            Your message has been sent successfully. We will get back to you shortly.
          </p>
          <motion.button
            onClick={() => setIsSubmitted(false)}
            className="mt-8 bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Send Another Message
          </motion.button>
        </motion.div>
      </section>
    );
  }

  return (
    <section id="contact" className="py-10 sm:py-10 bg-primary-75    font-sans">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* --- HEADER --- */}
        <motion.div
          ref={ref}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          variants={containerVariants}
          className="text-center mb-10"
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-5xl font-bold text-gray-900"
          >
            Get In Touch
          </motion.h2>
          <motion.div
            variants={itemVariants}
            className="w-32 sm:w-40 md:w-48 h-1 bg-orange-500 mx-auto mt-4 mb-6"
          />
          <motion.p
            variants={itemVariants}
            className="text-lg text-slate-600 max-w-3xl mx-auto"
          >
            Have a question or want to schedule a visit? Reach out to us. Our team is ready to assist you.
          </motion.p>
        </motion.div>

        {/* --- MAIN CONTACT CARD --- */}
        <motion.div
          ref={ref}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          variants={itemVariants}
          className="max-w-6xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden"
        >
          <div className="grid lg:grid-cols-2">
            {/* --- LEFT SIDE: INFO & IMAGE --- */}
            <div className="relative p-8 lg:p-12 bg-gray-800 text-white bg-[url('https://images.unsplash.com/photo-1596524430615-b46475ddff6e?q=80&w=2070')] bg-cover bg-center">
              <div className="absolute inset-0 bg-gray-900/60 backdrop-blur-sm"></div>
              <div className="relative z-10">
                <h3 className="text-3xl font-bold">Contact Information</h3>
                <p className="mt-2 text-gray-300">
                  Fill up the form and our team will get back to you within 24 hours.
                </p>

                <div className="mt-10 space-y-6">
                  {contactInfo.map((info) => (
                    <a
                      key={info.title}
                      href={info.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center group"
                    >
                      <info.icon className="h-6 w-6 text-orange-400 flex-shrink-0" />
                      <div className="ml-4">
                        <p className="font-semibold text-lg">{info.title}</p>
                        {info.details.map(detail => (
                           <span key={detail} className="text-gray-300 group-hover:text-white transition-colors">{detail}</span>
                        ))}
                      </div>
                    </a>
                  ))}
                </div>
                
                 <div className="mt-10 border-t border-gray-600 pt-6">
                   <a href="https://api.whatsapp.com/send?phone=919036699799" target="_blank" rel="noopener noreferrer" className="inline-flex items-center gap-3 px-6 py-3 bg-green-500 rounded-lg font-semibold hover:bg-green-600 transition-all duration-300">
                     <MessageCircle className="h-5 w-5"/>
                     Chat on WhatsApp
                   </a>
                 </div>
              </div>
            </div>

            {/* --- RIGHT SIDE: FORM --- */}
            <div className="p-8 lg:p-12">
              <h3 className="text-3xl font-bold text-gray-800">Send a Message</h3>
              <form onSubmit={handleSubmit} className="mt-8 space-y-6">
                {/* Name Input */}
                <div className="relative">
                  <User className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    name="name"
                    placeholder="Full Name"
                    required
                    onChange={handleInputChange}
                    value={formData.name}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all"
                  />
                </div>
                {/* Email Input */}
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="email"
                    name="email"
                    placeholder="Email Address"
                    required
                    onChange={handleInputChange}
                    value={formData.email}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all"
                  />
                </div>
                 {/* Phone Input */}
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="tel"
                    name="phone"
                    placeholder="Phone Number"
                    required
                    onChange={handleInputChange}
                    value={formData.phone}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all"
                  />
                </div>
                {/* Message Textarea */}
                <div className="relative">
                   <MessageSquare className="absolute left-3 top-4 h-5 w-5 text-gray-400" />
                  <textarea
                    name="message"
                    placeholder="Your Message..."
                    rows="5"
                    required
                    onChange={handleInputChange}
                    value={formData.message}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all resize-none"
                  ></textarea>
                </div>
                {/* Submit Button */}
                <motion.button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full px-6 py-4 bg-orange-600 text-white rounded-lg font-semibold text-lg flex items-center justify-center gap-3 disabled:opacity-60 disabled:cursor-not-allowed hover:bg-orange-700 transition-colors duration-300"
                  whileHover={{ scale: isSubmitting ? 1 : 1.02 }}
                  whileTap={{ scale: isSubmitting ? 1 : 0.98 }}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white" />
                      <span>Sending...</span>
                    </>
                  ) : (
                    <>
                      <Send className="h-5 w-5" />
                      <span>Send Message</span>
                    </>
                  )}
                </motion.button>
              </form>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Contact;