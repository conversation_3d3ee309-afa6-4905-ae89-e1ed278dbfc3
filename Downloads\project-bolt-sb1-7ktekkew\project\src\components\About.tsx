import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Award, Calendar, ShieldCheck, Home, Building, Users, Star } from "lucide-react";

// Animation variants for Framer Motion, providing a smooth fade-in and stagger effect.
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.6,
      ease: "easeInOut",
    },
  },
};

// Main 'About Us' component.
const About = () => {
  // Hook to trigger animations when the component scrolls into view.
  const { ref, inView } = useInView({ 
    triggerOnce: true, 
    threshold: 0.1 
  });

  // Data for the key information cards.
  const infoCards = [
    { icon: Calendar, title: "Established", value: "2010", description: "Over a decade of excellence" },
    { icon: Award, title: "RERA Approved", value: "Certified", description: "Ensuring transparency" },
    { icon: ShieldCheck, title: "DTCP Approved", value: "Verified", description: "Quality assurance" },
    { icon: Building, title: "Current Project", value: "Shreyas Sunrise", description: "Defining modern lifestyles" },
  ];

  return (
    <section id="about" className="py-20 md:py-10 bg-gray-50 text-slate-800 font-sans">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="w-full"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-black font-sans">About Shreyas Properties</h2>
          <div className="w-32 sm:w-40 md:w-48 h-1 mt-2 bg-orange-500 mx-auto rounded-full" ></div>
            <p className="mt-2 text-lg text-slate-600 max-w-3xl mx-auto">
              Building dreams and crafting lifestyles with trust and excellence since 2010.
            </p>
          </motion.div>

          {/* Main Content Grid: Text and Image */}
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            <motion.div variants={itemVariants} className="space-y-6 text-slate-600 text-lg leading-relaxed">
              <p>
                <strong className="text-slate-900">Shreyas Properties</strong> offers a fresh perspective on building the homes and projects of your dreams. Our vision is to deliver trusted, budget-friendly solutions that not only meet but exceed customer expectations.
              </p>
              <p>
                We leverage the latest technologies and stay ahead of market trends to ensure every venture is of the highest quality. Our experienced team is dedicated to creating attractive investment opportunities that are both financially viable and personally rewarding.
              </p>
              <p>
                Our ultimate goal is to ensure our customers invest not just in a plot of land, but in lasting happiness for their families. We are committed to a standard of excellence that permeates every aspect of our work.
              </p>
            </motion.div>
            <motion.div variants={itemVariants} className="w-full h-full">
              <img 
                src="https://placehold.co/600x450/f97316/ffffff?text=Shreyas+Properties&font=inter"
                alt="Modern housing project by Shreyas Properties"
                className="rounded-2xl object-cover w-full h-full shadow-xl transform hover:scale-105 transition-transform duration-500"
                onError={(e) => { e.target.onerror = null; e.target.src='https://placehold.co/600x450/e0e0e0/333333?text=Image+Not+Found'; }}
              />
            </motion.div>
          </div>
          
          {/* Information Cards Section */}
          <motion.div 
            variants={containerVariants}
            className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 mt-20"
          >
            {infoCards.map((card, index) => (
              <motion.div
                key={index}
                className="bg-white rounded-xl p-6 text-center shadow-md hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 border-t-4 border-transparent hover:border-orange-500"
                variants={itemVariants}
              >
                <div className="bg-orange-100 text-orange-500 rounded-full p-3 inline-block mb-4">
                  <card.icon className="h-8 w-8" />
                </div>
                <h4 className="font-bold text-slate-800 text-lg mb-1">
                  {card.title}
                </h4>
                <p className="text-orange-600 font-semibold text-base">{card.value}</p>
                <p className="text-sm text-slate-500 mt-1">{card.description}</p>
              </motion.div>
            ))}
          </motion.div>

        </motion.div>
      </div>
    </section>
  );
};

export default About;
