import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  Building,
  Users,
  Clock,
  Briefcase,
  IndianRupee,
  Rocket,
  ExternalLink,
} from "lucide-react";

// Animation variants for Framer Motion
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.15, delayChildren: 0.2 },
  },
};

const itemVariants = {
  hidden: { y: 30, opacity: 0 },
  visible: { y: 0, opacity: 1 },
};


// Helper component for employment hub cards
const HubCard = ({ hub }) => {
  const statusColors = {
    Operational: "bg-green-100 text-green-700 border-green-200",
    Expanding: "bg-blue-100 text-blue-700 border-blue-200",
    "Under Development": "bg-primary-75 text-amber-700 border-amber-200",
    Developing: "bg-purple-100 text-purple-700 border-purple-200",
  };

  return (
    <motion.div
      variants={itemVariants}
      className="bg-white rounded-2xl p-6 sm:p-8 flex flex-col transition-all duration-300 hover:shadow-xl border border-gray-200"
    >
      <div className="flex items-start space-x-5 mb-4">
        <div className="p-3 rounded-lg bg-slate-100 border border-slate-200">
          <hub.icon className="h-8 w-8 text-slate-600" />
        </div>
        <div className="flex-1">
          <div className="flex justify-between items-start mb-1">
            <div className="flex items-center space-x-2">
              <a
                href={hub.website}
                target="_blank"
                rel="noopener noreferrer"
                className="text-xl font-bold text-slate-800 hover:text-primary transition-colors duration-300 group flex items-center space-x-1"
                aria-label={`Visit ${hub.websiteLabel} (opens in new tab)`}
              >
                <span className="group-hover:underline">{hub.name}</span>
                <ExternalLink className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-primary" />
              </a>
            </div>
            <span
              className={`text-xs font-medium px-3 py-1 rounded-full whitespace-nowrap border ${
                statusColors[hub.status] || "bg-gray-100 text-gray-700 border-gray-200"
              }`}
            >
              {hub.status}
            </span>
          </div>
          <p className="text-sm text-slate-600">{hub.description}</p>
        </div>
      </div>

      <div className="mt-auto pt-4 grid grid-cols-2 gap-x-6 gap-y-4 border-t border-slate-200">
        <div className="flex items-center space-x-2">
          <IndianRupee className="w-5 h-5 text-primary" />
          <div>
            <p className="text-xs text-slate-500">Investment</p>
            <p className="text-base font-semibold text-slate-800">{hub.investment}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Clock className="w-5 h-5 text-primary" />
          <div>
            <p className="text-xs text-slate-500">Distance</p>
            <p className="text-base font-semibold text-slate-800">{hub.distance}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Users className="w-5 h-5 text-primary" />
          <div>
            <p className="text-xs text-slate-500">Employment</p>
            <p className="text-base font-semibold text-slate-800">{hub.employees}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Briefcase className="w-5 h-5 text-primary" />
          <div>
            <p className="text-xs text-slate-500">Sector</p>
            <p className="text-base font-semibold text-slate-800">{hub.type}</p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Main Component
const EmploymentHub = () => {
  // Intersection observer for scroll animations
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Data for the component
  const employmentHubs = [
    {
      name: "Foxconn iPhone Campus",
      investment: "₹22,000 Cr",
      distance: "15 mins",
      employees: "50,000+",
      type: "Manufacturing",
      description: "World's largest iPhone facility outside China.",
      icon: Building,
      color: "bg-blue-500/20 text-blue-300",
      status: "Under Development",
      website: "https://timesofindia.indiatimes.com/business/india-business/apples-biggest-contract-manufacturer-foxconn-readies-300-acre-iphone-making-campus-in-india-with-dorms-for-30000-employees/articleshow/121332453.cms",
      websiteLabel: "Foxconn Official Website"
    },
    {
      name: "SAP Labs New Campus",
      investment: "₹1,500 Cr",
      distance: "18 mins",
      employees: "15,000+",
      type: "IT Services",
      description: "Major software development and innovation center.",
      icon: Building,
      color: "bg-green-500/20 text-green-300",
      status: "Expanding",
      website: "https://www.thehindubusinessline.com/info-tech/sap-to-inaugurate-its-41-acre-campus-in-devanahalli-bengaluru-in-july-august/article69603507.ece",
      websiteLabel: "SAP Labs India"
    },
    {
      name: "Infosys Facility",
      investment: "₹700 Cr",
      distance: "20 mins",
      employees: "25,000+",
      type: "IT Services",
      description: "Large-scale IT services and development center.",
      icon: Building,
      color: "bg-purple-500/20 text-purple-300",
      status: "Operational",
      website: "https://www.business-standard.com/article/companies/infy-pulls-out-of-devanahalli-it-park-114110400015_1.html",
      websiteLabel: "Infosys Official Website"
    },
    {
      name: "Aerospace Park",
      investment: "₹5,000 Cr",
      distance: "12 mins",
      employees: "30,000+",
      type: "Aerospace",
      description: "India's largest aerospace & defense hub.",
      icon: Rocket,
      color: "bg-orange-500/20 text-orange-300",
      status: "Developing",
      website: "https://www.providentecopoliten.net.in/blog/what-is-aerospace-park-bangalore.html",
      websiteLabel: "KIADB Official Website"
    },
  ];


  return (
    <section
      id="employment"
      className="py-16 sm:py-10 bg-primary-75 font-sans"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-16"
        >
          {/* Header Section */}
          <motion.div variants={itemVariants} className="text-center space-y-4">
            <h2 className="text-4xl md:text-5xl font-bold text-black font-sans">
              Strategic Employment Corridor
            </h2>
            <div className="w-32 sm:w-40 md:w-48 h-1 bg-primary mx-auto rounded-full" />
            <p className="text-lg text-slate-600 max-w-3xl mx-auto leading-relaxed">
              Positioned at the epicenter of North Bengaluru's industrial transformation,
              Shreyas Sunrise benefits from{" "}
              <span className="font-bold text-primary">₹29,000+ Crores</span> in
              corporate investments, establishing this region as a premier employment
              and business destination.
            </p>
          </motion.div>


          {/* Employment Hubs Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {employmentHubs.map((hub, index) => (
              <HubCard key={index} hub={hub} />
            ))}
          </div>

        </motion.div>
      </div>
    </section>
  );
};

export default EmploymentHub;
