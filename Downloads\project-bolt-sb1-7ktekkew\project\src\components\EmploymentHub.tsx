import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  Building,
  Users,
  Clock,
  Briefcase,
  IndianRupee,
  Rocket,
  ExternalLink,
} from "lucide-react";

// Animation variants for the main container
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.1 },
  },
};

// Animation variants for individual items
const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};

// Helper component for the employment hub cards
const HubCard = ({ hub }) => {
  const statusColors = {
    Operational: "bg-green-100 text-green-800",
    Expanding: "bg-blue-100 text-blue-800",
    "Under Development": "bg-yellow-100 text-yellow-800",
    Developing: "bg-purple-100 text-purple-800",
  };

  return (
    <motion.div
      variants={itemVariants}
      className="bg-white rounded-xl p-6 flex flex-col transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 border-t-4 border-orange-500 shadow-lg"
    >
      {/* Card Header */}
      <div className="flex items-start space-x-4 mb-4">
        <div className="p-3 rounded-full bg-orange-100 text-orange-600">
          <hub.icon className="h-7 w-7" />
        </div>
        <div className="flex-1">
          <div className="flex justify-between items-start">
            <a
              href={hub.website}
              target="_blank"
              rel="noopener noreferrer"
              className="text-lg font-bold text-slate-800 hover:text-orange-600 transition-colors duration-300 group flex items-center space-x-1.5"
            >
              <span className="group-hover:underline">{hub.name}</span>
              <ExternalLink className="h-4 w-4 opacity-50 group-hover:opacity-100 transition-opacity" />
            </a>
            <span
              className={`text-xs font-bold px-2.5 py-1 rounded-full ${
                statusColors[hub.status] || "bg-gray-100 text-gray-800"
              }`}
            >
              {hub.status}
            </span>
          </div>
          <p className="text-sm text-slate-500">{hub.description}</p>
        </div>
      </div>

      {/* Card Stats */}
      <div className="mt-auto pt-4 grid grid-cols-2 gap-4 border-t border-slate-100">
        <div className="flex items-center space-x-2">
          <IndianRupee className="w-5 h-5 text-slate-400" />
          <div>
            <p className="text-xs text-slate-500">Investment</p>
            <p className="font-bold text-slate-700">{hub.investment}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Users className="w-5 h-5 text-slate-400" />
          <div>
            <p className="text-xs text-slate-500">Employment</p>
            <p className="font-bold text-slate-700">{hub.employees}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Clock className="w-5 h-5 text-slate-400" />
          <div>
            <p className="text-xs text-slate-500">Distance</p>
            <p className="font-bold text-slate-700">{hub.distance}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Briefcase className="w-5 h-5 text-slate-400" />
          <div>
            <p className="text-xs text-slate-500">Sector</p>
            <p className="font-bold text-slate-700">{hub.type}</p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};


// Main Employment Hub Component
const EmploymentHub = () => {
  // Intersection observer for scroll animations
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Data for the employment hubs
  const employmentHubs = [
    {
      name: "Foxconn iPhone Campus",
      investment: "₹22,000 Cr",
      distance: "15 mins",
      employees: "50,000+",
      type: "Manufacturing",
      description: "World's largest iPhone facility outside China.",
      icon: Building,
      status: "Under Development",
      website: "#",
    },
    {
      name: "SAP Labs New Campus",
      investment: "₹1,500 Cr",
      distance: "18 mins",
      employees: "15,000+",
      type: "IT Services",
      description: "Major software development and innovation center.",
      icon: Building,
      status: "Expanding",
      website: "#",
    },
    {
      name: "Infosys Facility",
      investment: "₹700 Cr",
      distance: "20 mins",
      employees: "25,000+",
      type: "IT Services",
      description: "Large-scale IT services and development center.",
      icon: Building,
      status: "Operational",
      website: "#",
    },
    {
      name: "Aerospace Park",
      investment: "₹5,000 Cr",
      distance: "12 mins",
      employees: "30,000+",
      type: "Aerospace",
      description: "India's largest aerospace & defense hub.",
      icon: Rocket,
      status: "Developing",
      website: "#",
    },
  ];


  return (
    <section id="employment" className="py-20 md:py-10 bg-primary-75 font-sans">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-16"
        >
          {/* Section Header */}
          <div className="text-center">
            <motion.h2 varian ts={itemVariants} className="text-4xl md:text-5xl font-bold text-slate-900">
              Strategic Employment Corridor
            </motion.h2>
            <motion.div variants={itemVariants} className="w-32 sm:w-40 md:w-48 h-1 mt-2 bg-orange-500 mx-auto rounded-full" />
            <motion.p variants={itemVariants} className="mt-4 text-lg text-slate-600 max-w-3xl mx-auto">
              Positioned at the epicenter of North Bengaluru's industrial transformation, with over <span className="font-bold text-orange-600">₹29,000 Crores</span> in corporate investments.
            </motion.p>
          </div>

          {/* Employment Hubs Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {employmentHubs.map((hub, index) => (
              <HubCard key={index} hub={hub} />
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default EmploymentHub;
