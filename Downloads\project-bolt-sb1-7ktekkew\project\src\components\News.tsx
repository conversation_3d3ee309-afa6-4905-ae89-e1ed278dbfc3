import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Newspaper, TrendingUp, Building2, Plane, ArrowUpRight } from "lucide-react";

// Animation variants for the main container
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.15 },
  },
};

// Animation variants for individual news items
const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};

// Main News Component
const News = () => {
  // Hook to trigger animations when the component scrolls into view
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Data for the news articles
  const newsItems = [
    {
      icon: Building2,
      title: "Information Technology Investment Regions (ITIR)",
      content: "An integrated IT city across 10,000 acres in Devanahalli, Chickballapur, and nearby regions, set to transform North Bangalore.",
      highlight: "10,000 Acres",
      link: "https://www.projectstoday.com/News/Karnataka-proposes-ITIR-near-Devanahalli" // Added link
    },
    {
      icon: Plane,
      title: "Aerospace SEZ in Devanahalli",
      content: "Aircraft maintenance, repair, and overhaul units to manufacture spare parts. Airbus and Lufthansa are some major players showing interest.",
      highlight: "Aviation Hub",
      link: "https://www.industryexperts.co.in/industrial-area-profile/bengaluru-aerospace-sez/karnataka"
    },
    {
      icon: TrendingUp,
      title: "Global Investors Meet Impact",
      content: "The Global Investors Meet brought the area into focus, attracting keen interest for IT Parks, SEZs, hotels, and prestigious residential projects.",
      highlight: "Investment Surge",
      link: "https://www.onecityproperty.com/news/property-market-and-investment-growth-in-devanahalli-bangalore" 
    },
    {
      icon: Newspaper,
      title: "Infrastructure Development",
      content: "With increasing passenger traffic and government initiatives, the airport is poised to become South India's busiest hub for all air traffic.",
      highlight: "South India's Hub",
      link: "https://bangaloremirror.indiatimes.com/bangalore/others/bm-property-devanahallis-infrastructure-set-for-a-big-boost/articleshow/118458757.cms" 
    }
  ];

  return (
    <section id="news" className="py-20 md:py-10 bg-gray-50 font-sans">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
        >
          {/* Section Header */}
          <div className="text-center mb-16">
            <motion.h2
              variants={itemVariants}
              className="text-4xl md:text-5xl font-bold text-slate-900"
            >
              North Bangalore News
            </motion.h2>
            <motion.div variants={itemVariants} className="w-32 sm:w-40 md:w-48 h-1 mt-2 bg-orange-500 mx-auto rounded-full" />
            <motion.p
              variants={itemVariants}
              className="mt-4 text-lg text-slate-600 max-w-3xl mx-auto"
            >
              Stay updated with the latest developments and growth opportunities in the region.
            </motion.p>
          </div>

          {/* News Items Grid */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 md:grid-cols-2 gap-8"
          >
            {newsItems.map((item, index) => (
              <motion.a
                key={index}
                href={item.link}
                target="_blank"
                rel="noopener noreferrer"
                variants={itemVariants}
                className="block bg-white rounded-xl p-6 shadow-lg hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 group border-l-4 border-orange-500"
              >
                <div className="flex items-start space-x-5">
                  {/* Icon */}
                  <div className="flex-shrink-0 bg-orange-100 text-orange-600 p-4 rounded-full group-hover:bg-orange-500 group-hover:text-white transition-all duration-300">
                    <item.icon size={24} />
                  </div>
                  
                  {/* Content */}
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2 flex-wrap">
                      <h3 className="font-bold text-lg text-slate-800 group-hover:text-orange-600 transition-colors duration-300">
                        {item.title}
                      </h3>
                      <span className="bg-orange-100 text-orange-700 text-xs px-3 py-1 rounded-full font-semibold mt-2 sm:mt-0">
                        {item.highlight}
                      </span>
                    </div>
                    <p className="text-sm text-slate-600 leading-relaxed">
                      {item.content}
                    </p>
                     <div className="flex items-center mt-4 text-sm font-semibold text-orange-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        Read More <ArrowUpRight className="ml-1 h-4 w-4" />
                    </div>
                  </div>
                </div>
              </motion.a>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default News;
