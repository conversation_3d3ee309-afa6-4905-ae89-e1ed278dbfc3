import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { X, ChevronLeft, ChevronRight, ZoomIn } from "lucide-react";

// Animation variants for the main container
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

// Animation variants for individual grid items
const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.6,
      ease: "easeInOut",
    },
  },
};

// Animation for the lightbox backdrop
const backdropVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
};

// Animation variants for the image slider
// This controls the direction of the slide animation
const slideVariants = {
  enter: (direction: number) => {
    return {
      x: direction > 0 ? 1000 : -1000,
      opacity: 0,
    };
  },
  center: {
    zIndex: 1,
    x: 0,
    opacity: 1,
  },
  exit: (direction: number) => {
    return {
      zIndex: 0,
      x: direction < 0 ? 1000 : -1000,
      opacity: 0,
    };
  },
};


// Main Gallery Component
const Gallery = () => {
  // Hook to trigger animations when the component is in view
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // State to manage the currently selected image and navigation direction
  const [[page, direction], setPage] = useState([0, 0]);
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);


  // Data for the gallery images, using placeholders
  const galleryImages = [
    {
      id: 1,
      src: "https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=800",
      alt: "Shreyas Sunrise - Entrance Gate",
      title: "Premium Entrance"
    },
    {
      id: 2,
      src: "https://images.pexels.com/photos/1475938/pexels-photo-1475938.jpeg?auto=compress&cs=tinysrgb&w=800",
      alt: "Shreyas Sunrise - Layout Plan",
      title: "Master Layout"
    },
    {
      id: 3,
      src: "https://images.pexels.com/photos/2121121/pexels-photo-2121121.jpeg?auto=compress&cs=tinysrgb&w=800",
      alt: "Shreyas Sunrise - Clubhouse",
      title: "Clubhouse Facility"
    },
    {
      id: 4,
      src: "https://images.pexels.com/photos/1571468/pexels-photo-1571468.jpeg?auto=compress&cs=tinysrgb&w=800",
      alt: "Shreyas Sunrise - Swimming Pool",
      title: "Swimming Pool"
    },
    {
      id: 5,
      src: "https://images.pexels.com/photos/2102587/pexels-photo-2102587.jpeg?auto=compress&cs=tinysrgb&w=800",
      alt: "Shreyas Sunrise - Landscaping",
      title: "Landscaped Gardens"
    },
    {
      id: 6,
      src: "https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=800",
      alt: "Shreyas Sunrise - Sports Facilities",
      title: "Sports Complex"
    },
    {
      id: 7,
      src: "https://images.pexels.com/photos/2102588/pexels-photo-2102588.jpeg?auto=compress&cs=tinysrgb&w=800",
      alt: "Shreyas Sunrise - Children's Play Area",
      title: "Kids Play Zone"
    },
    {
      id: 8,
      src: "https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800",
      alt: "Shreyas Sunrise - Road Infrastructure",
      title: "Internal Roads"
    }
  ];

  // The active image index
  const imageIndex = (page % galleryImages.length + galleryImages.length) % galleryImages.length;

  // Functions to handle lightbox state and navigation
  const paginate = (newDirection: number) => {
    setPage([page + newDirection, newDirection]);
  };

  const openLightbox = (index: number) => {
    setPage([index, 0]);
    setIsLightboxOpen(true);
  };

  const closeLightbox = () => {
    setIsLightboxOpen(false);
  };

  // Effect to handle keyboard navigation for the lightbox
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isLightboxOpen) return;
      if (e.key === 'ArrowRight') paginate(1);
      if (e.key === 'ArrowLeft') paginate(-1);
      if (e.key === 'Escape') closeLightbox();
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isLightboxOpen, page]);


  return (
    <section id="gallery" className="py-20 md:py-10 bg-gray-50 font-sans">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="text-center mb-16"
        >
          <motion.h2 variants={itemVariants} className="text-4xl md:text-5xl font-bold text-slate-900">
            Project Gallery
          </motion.h2>
          <motion.div variants={itemVariants} className="w-32 sm:w-40 md:w-48 h-1 mt-2 bg-orange-500 mx-auto rounded-full" />
          <motion.p variants={itemVariants} className="mt-4 text-lg text-slate-600 max-w-3xl mx-auto">
            Explore our premium development, showcasing quality infrastructure, amenities, and beautiful landscapes.
          </motion.p>
        </motion.div>

        {/* Image Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
        >
          {galleryImages.map((image, index) => (
            <motion.div
              key={image.id}
              variants={itemVariants}
              className="relative group cursor-pointer overflow-hidden rounded-xl shadow-lg"
              onClick={() => openLightbox(index)}
            >
              <img
                src={image.src}
                alt={image.alt}
                className="w-full h-64 object-cover transition-transform duration-500 ease-in-out group-hover:scale-110"
                onError={(e) => { e.currentTarget.onerror = null; e.currentTarget.src='https://placehold.co/800x600/e0e0e0/333333?text=Image+Error'; }}
              />
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-all duration-300 flex flex-col items-center justify-center p-4">
                <ZoomIn className="h-10 w-10 text-white opacity-0 group-hover:opacity-80 transform scale-75 group-hover:scale-100 transition-all duration-300" />
                <p className="font-bold text-white text-lg mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 delay-100">
                  {image.title}
                </p>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Lightbox Modal with Slider */}
        <AnimatePresence>
          {isLightboxOpen && (
            <motion.div
              variants={backdropVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
              className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center"
              onClick={closeLightbox}
            >
              {/* Image Slider Container */}
              <div className="relative w-full max-w-4xl h-[80vh] flex items-center justify-center">
                <AnimatePresence initial={false} custom={direction}>
                  <motion.img
                    key={page}
                    src={galleryImages[imageIndex].src}
                    alt={galleryImages[imageIndex].alt}
                    custom={direction}
                    variants={slideVariants}
                    initial="enter"
                    animate="center"
                    exit="exit"
                    transition={{
                      x: { type: "spring", stiffness: 300, damping: 30 },
                      opacity: { duration: 0.2 },
                    }}
                    className="absolute max-w-full max-h-full object-contain rounded-lg shadow-2xl"
                    onClick={(e) => e.stopPropagation()}
                  />
                </AnimatePresence>
              </div>

              {/* Close Button */}
              <button
                onClick={closeLightbox}
                className="absolute top-4 right-4 md:top-6 md:right-6 text-white/70 hover:text-white transition-colors z-50 bg-black/20 rounded-full p-2"
              >
                <X size={28} />
              </button>

              {/* Navigation Buttons */}
              <button
                onClick={(e) => { e.stopPropagation(); paginate(-1); }}
                className="absolute left-4 top-1/2 -translate-y-1/2 text-white/70 hover:text-white bg-black/20 hover:bg-black/40 rounded-full p-3 transition-all z-50"
              >
                <ChevronLeft size={32} />
              </button>
              <button
                onClick={(e) => { e.stopPropagation(); paginate(1); }}
                className="absolute right-4 top-1/2 -translate-y-1/2 text-white/70 hover:text-white bg-black/20 hover:bg-black/40 rounded-full p-3 transition-all z-50"
              >
                <ChevronRight size={32} />
              </button>
              
              {/* Image Info */}
              <div className="absolute bottom-6 left-1/2 -translate-x-1/2 text-center text-white bg-black/30 px-4 py-2 rounded-lg">
                  <h3 className="font-bold text-lg">{galleryImages[imageIndex].title}</h3>
                  <p className="text-sm opacity-80">{imageIndex + 1} / {galleryImages.length}</p>
              </div>

            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </section>
  );
};

export default Gallery;
