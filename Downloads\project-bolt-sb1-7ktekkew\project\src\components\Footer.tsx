import React from "react";
import { motion } from "framer-motion";
import {
  Phone,
  Mail,
  MapPin,
  Facebook,
  Linkedin,
  Youtube,
} from "lucide-react";

// --- FOOTER COMPONENT ---
const Footer = () => {

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  const socialLinks = [
    { icon: Facebook, href: "https://www.facebook.com/ShreyasProperties/", label: "Facebook" },
    { icon: Youtube, href: "https://www.youtube.com/watch?v=DaUaY7KRrFI&t=1s", label: "YouTube" },
    { icon: Linkedin, href: "https://www.linkedin.com/company/shreyassunrise/", label: "LinkedIn" },
  ];

  const quickLinks = [
    { name: "Home", href: "#home" },
    { name: "About", href: "#about" },
    { name: "Projects", href: "#properties" },
    { name: "Why Invest", href: "#why-invest" },
    { name: "Contact", href: "#contact" },
  ];

  const services = [
    "Site Visits",
    "Investment Consultation",
    "RERA Approved Projects",
    "DTCP Approved",
    "Premium Amenities",
  ];

  return (
    <footer className="bg-charcoal-900 text-slate-300 font-sans">
      {/* Main Footer */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Brand Section */}
          <div className="space-y-6">
            <div>
              <h3 className="text-2xl font-bold text-white font-heading">
                Shreyas Properties
              </h3>
              <p className="text-primary-400 font-accent font-semibold">
                defining lifestyles
              </p>
            </div>
            <p className="text-slate-400 text-body leading-relaxed">
              Building premium, sustainable properties that offer exceptional living experiences and sound investment opportunities in North Bangalore.
            </p>
            <div className="flex space-x-3">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={social.label}
                  className="w-10 h-10 bg-charcoal-800 rounded-full flex items-center justify-center text-slate-400 hover:bg-primary-600 hover:text-white transition-all duration-300 shadow-card hover:shadow-card-hover"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <social.icon className="h-5 w-5" />
                </motion.a>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-6">
            <h4 className="text-lg font-bold text-white font-heading">Quick Links</h4>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <button
                    onClick={() => scrollToSection(link.href)}
                    className="text-slate-400 hover:text-primary-400 hover:pl-1 transition-all duration-300 text-left font-body"
                  >
                    {link.name}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div className="space-y-6">
            <h4 className="text-lg font-bold text-white font-heading">Our Services</h4>
            <ul className="space-y-3">
              {services.map((service, index) => (
                <li key={index} className="text-slate-400 font-body flex items-center">
                  <span className="w-2 h-2 bg-primary-500 rounded-full mr-3 flex-shrink-0"></span>
                  {service}
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-6">
            <h4 className="text-lg font-bold text-white font-heading">Contact Info</h4>
            <div className="space-y-4 text-body-sm">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-primary-400 flex-shrink-0 mt-1" />
                <p className="text-slate-400 font-body">
                  7-8/1, 4th Main, 4th block, Kalyan Nagar, Bengaluru, Karnataka 560043
                </p>
              </div>
              <div className="flex items-start space-x-3">
                <Phone className="h-5 w-5 text-primary-400 flex-shrink-0 mt-1" />
                <div>
                  <a href="tel:+919036699799" className="text-slate-400 hover:text-primary-400 transition-colors duration-300 block font-body">
                    +91-9036699799
                  </a>
                  <a href="tel:+918151884545" className="text-slate-400 hover:text-primary-400 transition-colors duration-300 block font-body">
                    +91-8151884545
                  </a>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Mail className="h-5 w-5 text-primary-400 flex-shrink-0 mt-1" />
                <a
                  href="mailto:<EMAIL>"
                  className="text-slate-400 hover:text-primary-400 transition-colors duration-300 font-body"
                >
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-charcoal-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-slate-500 text-body-sm text-center md:text-left font-body">
              © {new Date().getFullYear()} Shreyas Properties. All rights reserved.
            </p>
            <div className="flex space-x-6">
                <a href="#" className="text-slate-500 hover:text-primary-400 text-body-sm font-body transition-colors duration-300">Privacy Policy</a>
                <a href="#" className="text-slate-500 hover:text-primary-400 text-body-sm font-body transition-colors duration-300">Terms of Service</a>
            </div>
          </div>
        </div>
      </div>



    </footer>
  );
};

export default Footer;
