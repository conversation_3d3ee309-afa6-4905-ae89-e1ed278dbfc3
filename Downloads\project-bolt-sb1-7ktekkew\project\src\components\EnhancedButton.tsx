import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';

interface EnhancedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  icon?: LucideIcon;
  iconPosition?: 'left' | 'right';
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  href?: string;
  target?: string;
  rel?: string;
}

const EnhancedButton: React.FC<EnhancedButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  icon: Icon,
  iconPosition = 'left',
  disabled = false,
  loading = false,
  className = '',
  href,
  target,
  rel
}) => {
  const baseClasses = 'relative inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 overflow-hidden group';
  
  const variantClasses = {
    primary: 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg hover:shadow-xl focus:ring-orange-500 border border-orange-500/20',
    secondary: 'bg-gradient-to-r from-slate-600 to-slate-700 text-white shadow-lg hover:shadow-xl focus:ring-slate-500 border border-slate-500/20',
    outline: 'border-2 border-orange-500 text-orange-600 bg-transparent hover:bg-orange-500 hover:text-white focus:ring-orange-500',
    ghost: 'text-orange-600 bg-transparent hover:bg-orange-50 focus:ring-orange-500'
  };
  
  const sizeClasses = {
    sm: 'px-4 py-2 text-sm gap-2',
    md: 'px-6 py-3 text-base gap-2',
    lg: 'px-8 py-4 text-lg gap-3'
  };
  
  const disabledClasses = 'opacity-50 cursor-not-allowed';
  
  const classes = `
    ${baseClasses}
    ${variantClasses[variant]}
    ${sizeClasses[size]}
    ${disabled ? disabledClasses : ''}
    ${className}
  `.trim();

  const buttonContent = (
    <>
      {/* Background animation */}
      <motion.div
        className={`absolute inset-0 ${
          variant === 'primary' 
            ? 'bg-gradient-to-r from-orange-400 to-orange-500' 
            : variant === 'secondary'
            ? 'bg-gradient-to-r from-slate-500 to-slate-600'
            : 'bg-orange-500'
        }`}
        initial={{ x: "-100%" }}
        whileHover={{ x: disabled ? "-100%" : 0 }}
        transition={{ duration: 0.3 }}
      />
      
      {/* Content */}
      <span className="relative z-10 flex items-center gap-2">
        {Icon && iconPosition === 'left' && (
          <motion.span
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ duration: 0.2 }}
          >
            <Icon className={`${size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5'}`} />
          </motion.span>
        )}
        
        {loading ? (
          <motion.div
            className={`${size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5'} border-2 border-current border-t-transparent rounded-full`}
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
        ) : (
          children
        )}
        
        {Icon && iconPosition === 'right' && (
          <motion.span
            whileHover={{ scale: 1.1, rotate: -5 }}
            transition={{ duration: 0.2 }}
          >
            <Icon className={`${size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5'}`} />
          </motion.span>
        )}
      </span>
      
      {/* Ripple effect */}
      <motion.div
        className="absolute inset-0 bg-white/20 rounded-full scale-0 group-active:scale-100"
        transition={{ duration: 0.2 }}
      />
    </>
  );

  const motionProps = {
    whileHover: disabled ? {} : { scale: 1.02, y: -2 },
    whileTap: disabled ? {} : { scale: 0.98, y: 0 },
    transition: { duration: 0.2, ease: "easeOut" }
  };

  if (href) {
    return (
      <motion.a
        href={href}
        target={target}
        rel={rel}
        className={classes}
        {...motionProps}
      >
        {buttonContent}
      </motion.a>
    );
  }

  return (
    <motion.button
      onClick={disabled ? undefined : onClick}
      disabled={disabled}
      className={classes}
      {...motionProps}
    >
      {buttonContent}
    </motion.button>
  );
};

export default EnhancedButton;
