import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  Waves,
  Utensils,
  Dumbbell,
  Car,
  Wifi,
  Shield,
  Zap,
  TreePine,
  Wine,
  Camera,
  Headphones,
  Thermometer,
} from "lucide-react";

// --- ANIMATION VARIANTS (Unchanged) ---
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.1, delayChildren: 0.2 },
  },
};

const itemVariants = {
  hidden: { y: 50, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { duration: 0.6, ease: "easeOut" },
  },
};

// --- AMENITIES COMPONENT (Updated for Conditional Rendering) ---
const Amenities = () => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // --- DATA: Set imageUrl to 'null' for amenities without an image ---
  const amenities = [
    {
      icon: Waves,
      title: "Infinity Pool",
      description: "Spectacular infinity pool with panoramic views.",
      imageUrl: "https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?q=80&w=2070",
    },
    {
      icon: Utensils,
      title: "Gourmet Kitchen",
      description: "Professional-grade appliances and custom cabinetry.",
      imageUrl: "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?q=80&w=2070",
    },
    {
      icon: Dumbbell,
      title: "Private Gym",
      description: "Fully equipped fitness center with modern equipment.",
      imageUrl: "https://images.unsplash.com/photo-1584735935682-2f2b69dff9d2?q=80&w=2071",
    },
    {
      icon: Car,
      title: "Secure Parking",
      description: "Ample covered parking with EV charging stations.",
      imageUrl: "https://images.unsplash.com/photo-1600585154526-990dced4db0d?q=80&w=2070"
    },
    {
      icon: Wifi,
      title: "Smart Home",
      description: "Integrated automation and high-speed internet.",
      imageUrl: "https://images.unsplash.com/photo-1558002038-1055907df827?q=80&w=2070",
    },
    {
      icon: Shield,
      title: "24/7 Security",
      description: "Advanced monitoring and professional security staff.",
      imageUrl: "https://media.istockphoto.com/id/1618446911/photo/radio-man-and-a-security-guard-or-safety-officer-outdoor-on-a-city-road-for-communication.jpg?s=612x612&w=0&k=20&c=kEQBzw8KmJG4JRWlEWi04dwR651olCzW4k1MKsQaxyg=" 
    },
    {
      icon: Zap,
      title: "Power Backup",
      description: "100% power backup for all common areas and homes.",
      imageUrl: "https://media.istockphoto.com/id/1463676618/vector/isometric-service-engineer-repairing-or-adjusting-diesel-power-generator-portable-electric.jpg?s=612x612&w=0&k=20&c=YoX0sLYXEVCJqklkjr6jbBb7ec4pAdZAdYlRdNXImS4="
    },
    {
      icon: TreePine,
      title: "Landscaped Gardens",
      description: "Beautifully manicured gardens and walking trails.",
      imageUrl:"https://teja10.kuikr.com//r1/20190913/ak_755_1422299621-1568369109_700x700.jpeg"
    },
     {
      icon: Wine,
      title: "Clubhouse",
      description: "Elegant clubhouse with lounge and party hall.",
      imageUrl: "https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?auto=format&fit=crop&w=500&q=80"
    },
    {
      icon: Camera,
      title: "Media Room",
      description: "Home theater with a premium sound system.",
      imageUrl: "https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?q=80&w=2070",
    },
    {
      icon: Headphones,
      title: "Music Studio",
      description: "Soundproofed recording and practice space.",
      imageUrl: "https://images.unsplash.com/photo-1511379938547-c1f69419868d?q=80&w=2070",
    },
    {
      icon: Thermometer,
      title: "Spa & Wellness",
      description: "Private spa featuring a sauna and massage room.",
      imageUrl: "https://images.unsplash.com/photo-1540555700478-4be289fbecef?q=80&w=2070",
    },
  ];

  return (
    <section id="amenities" className="py-16 sm:py-20 bg-white font-sans">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          variants={containerVariants}
          className="text-center mb-12"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Luxury Amenities
          </h2>
          <div className="w-32 sm:w-40 md:w-48 h-1 bg-orange-500 mx-auto mb-6" />
          <p className="text-lg text-slate-600 max-w-3xl mx-auto">
            Experience a life of comfort and convenience with our world-class amenities, designed to cater to your every need.
          </p>
        </motion.div>

        <motion.div
          ref={ref}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          variants={containerVariants}
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          {amenities.map((amenity) => (
            <motion.div key={amenity.title} variants={itemVariants} className="h-full">
              {/* === CONDITIONAL RENDERING LOGIC === */}
              {amenity.imageUrl ? (
                // --- CARD WITH IMAGE ---
                <div className="bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden h-full flex flex-col">
                  <img
                    src={amenity.imageUrl}
                    alt={amenity.title}
                    className="w-full h-40 object-cover"
                  />
                  <div className="p-5 flex flex-col flex-grow">
                    <div className="flex items-center mb-2">
                      <amenity.icon className="h-5 w-5 mr-2 text-orange-500" />
                      <h3 className="text-base font-bold text-gray-800">{amenity.title}</h3>
                    </div>
                    <p className="text-sm text-slate-600 flex-grow">{amenity.description}</p>
                  </div>
                </div>
              ) : (
                // --- CARD WITHOUT IMAGE (PLACEHOLDER) ---
                <div className="bg-slate-50 border border-slate-200 rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 h-full flex flex-col justify-center relative p-5">
                   <div className="absolute top-4 right-5 text-slate-400 text-sm font-medium flex items-center">
                     <amenity.icon className="h-4 w-4 mr-2" />
                     {amenity.title}
                   </div>
                  <div className="mt-8"> {/* Added margin-top to push content down */}
                    <div className="flex items-center mb-2">
                        <amenity.icon className="h-5 w-5 mr-2 text-orange-500" />
                        <h3 className="text-base font-bold text-gray-800">{amenity.title}</h3>
                    </div>
                    <p className="text-sm text-slate-600">{amenity.description}</p>
                  </div>
                </div>
              )}
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default Amenities;