import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  Waves,
  Utensils,
  Dumbbell,
  Car,
  Wifi,
  Shield,
  Zap,
  TreePine,
  Wine,
  Camera,
  Headphones,
  Thermometer,
} from "lucide-react";

// --- ENHANCED ANIMATION VARIANTS ---
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.08, delayChildren: 0.2 },
  },
};

const itemVariants = {
  hidden: { y: 60, opacity: 0, scale: 0.9 },
  visible: {
    y: 0,
    opacity: 1,
    scale: 1,
    transition: { duration: 0.7 },
  },
};

// --- AMENITIES COMPONENT (Updated for Conditional Rendering) ---
const Amenities = () => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // --- DATA: Set imageUrl to 'null' for amenities without an image ---
  const amenities = [
    {
      icon: Waves,
      title: "Infinity Pool",
      description: "Spectacular infinity pool with panoramic views.",
      imageUrl: "https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?q=80&w=2070",
    },
    {
      icon: Utensils,
      title: "Gourmet Kitchen",
      description: "Professional-grade appliances and custom cabinetry.",
      imageUrl: "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?q=80&w=2070",
    },
    {
      icon: Dumbbell,
      title: "Private Gym",
      description: "Fully equipped fitness center with modern equipment.",
      imageUrl: "https://images.unsplash.com/photo-1584735935682-2f2b69dff9d2?q=80&w=2071",
    },
    {
      icon: Car,
      title: "Secure Parking",
      description: "Ample covered parking with EV charging stations.",
      imageUrl: "https://images.unsplash.com/photo-1600585154526-990dced4db0d?q=80&w=2070"
    },
    {
      icon: Wifi,
      title: "Smart Home",
      description: "Integrated automation and high-speed internet.",
      imageUrl: "https://images.unsplash.com/photo-1558002038-1055907df827?q=80&w=2070",
    },
    {
      icon: Shield,
      title: "24/7 Security",
      description: "Advanced monitoring and professional security staff.",
      imageUrl: "https://media.istockphoto.com/id/1618446911/photo/radio-man-and-a-security-guard-or-safety-officer-outdoor-on-a-city-road-for-communication.jpg?s=612x612&w=0&k=20&c=kEQBzw8KmJG4JRWlEWi04dwR651olCzW4k1MKsQaxyg=" 
    },
    {
      icon: Zap,
      title: "Power Backup",
      description: "100% power backup for all common areas and homes.",
      imageUrl: "https://media.istockphoto.com/id/1463676618/vector/isometric-service-engineer-repairing-or-adjusting-diesel-power-generator-portable-electric.jpg?s=612x612&w=0&k=20&c=YoX0sLYXEVCJqklkjr6jbBb7ec4pAdZAdYlRdNXImS4="
    },
    {
      icon: TreePine,
      title: "Landscaped Gardens",
      description: "Beautifully manicured gardens and walking trails.",
      imageUrl:"https://teja10.kuikr.com//r1/20190913/ak_755_1422299621-1568369109_700x700.jpeg"
    },
     {
      icon: Wine,
      title: "Clubhouse",
      description: "Elegant clubhouse with lounge and party hall.",
      imageUrl: "https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?auto=format&fit=crop&w=500&q=80"
    },
    {
      icon: Camera,
      title: "Media Room",
      description: "Home theater with a premium sound system.",
      imageUrl: "https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?q=80&w=2070",
    },
    {
      icon: Headphones,
      title: "Music Studio",
      description: "Soundproofed recording and practice space.",
      imageUrl: "https://images.unsplash.com/photo-1511379938547-c1f69419868d?q=80&w=2070",
    },
    {
      icon: Thermometer,
      title: "Spa & Wellness",
      description: "Private spa featuring a sauna and massage room.",
      imageUrl: "https://images.unsplash.com/photo-1540555700478-4be289fbecef?q=80&w=2070",
    },
  ];

  return (
    <section id="amenities" className="py-16 sm:py-10  bg-gray-50 font-sans">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          variants={containerVariants}
          className="text-center mb-12"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Luxury Amenities
          </h2>
          <div className="w-32 sm:w-40 md:w-48 h-1 bg-orange-500 mx-auto mb-6" />
          <p className="text-lg text-slate-600 max-w-3xl mx-auto">
            Experience a life of comfort and convenience with our world-class amenities, designed to cater to your every need.
          </p>
        </motion.div>

        <motion.div
          ref={ref}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          variants={containerVariants}
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          {amenities.map((amenity, index) => (
            <motion.div
              key={amenity.title}
              variants={itemVariants}
              className="h-full"
              whileHover={{ y: -8, scale: 1.02 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
            >
              {/* === CONDITIONAL RENDERING LOGIC === */}
              {amenity.imageUrl ? (
                // --- ENHANCED CARD WITH IMAGE ---
                <motion.div
                  className="group bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden h-full flex flex-col relative"
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  {/* Image container with overlay */}
                  <div className="relative overflow-hidden">
                    <motion.img
                      src={amenity.imageUrl}
                      alt={amenity.title}
                      className="w-full h-48 object-cover transition-transform duration-700 group-hover:scale-110"
                    />

                    {/* Gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                    {/* Floating icon */}
                    <motion.div
                      className="absolute top-4 right-4 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ duration: 0.2 }}
                    >
                      <amenity.icon className="h-6 w-6 text-orange-500" />
                    </motion.div>
                  </div>

                  {/* Content */}
                  <div className="p-6 flex flex-col flex-grow">
                    <motion.h3
                      className="text-lg font-bold text-gray-800 mb-3 group-hover:text-orange-600 transition-colors duration-300"
                      whileHover={{ x: 4 }}
                    >
                      {amenity.title}
                    </motion.h3>
                    <p className="text-sm text-slate-600 flex-grow leading-relaxed">
                      {amenity.description}
                    </p>

                    {/* Animated bottom border */}
                    <motion.div
                      className="mt-4 h-1 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full"
                      initial={{ width: 0 }}
                      whileInView={{ width: "100%" }}
                      transition={{ duration: 0.8, delay: index * 0.1 }}
                    />
                  </div>

                  {/* Hover glow effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-orange-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
                  />
                </motion.div>
              ) : (
                // --- ENHANCED CARD WITHOUT IMAGE ---
                <motion.div
                  className="group bg-gradient-to-br from-slate-50 to-slate-100 border border-slate-200 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 h-full flex flex-col justify-center relative p-6"
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  {/* Background pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-500 to-orange-600" />
                  </div>

                  {/* Floating icon */}
                  <motion.div
                    className="absolute top-4 right-4 w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center"
                    whileHover={{ scale: 1.1, rotate: 10 }}
                    transition={{ duration: 0.2 }}
                  >
                    <amenity.icon className="h-5 w-5 text-orange-500" />
                  </motion.div>

                  <div className="relative z-10">
                    <motion.h3
                      className="text-lg font-bold text-gray-800 mb-3 group-hover:text-orange-600 transition-colors duration-300"
                      whileHover={{ x: 4 }}
                    >
                      {amenity.title}
                    </motion.h3>
                    <p className="text-sm text-slate-600 leading-relaxed">
                      {amenity.description}
                    </p>

                    {/* Animated accent line */}
                    <motion.div
                      className="mt-4 h-1 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full"
                      initial={{ width: 0 }}
                      whileInView={{ width: "60%" }}
                      transition={{ duration: 0.8, delay: index * 0.1 }}
                    />
                  </div>
                </motion.div>
              )}
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default Amenities;