import React from 'react';
import { motion } from 'framer-motion';

interface LoadingSkeletonProps {
  variant?: 'card' | 'text' | 'avatar' | 'button' | 'image';
  width?: string | number;
  height?: string | number;
  className?: string;
  count?: number;
}

const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({
  variant = 'text',
  width,
  height,
  className = '',
  count = 1
}) => {
  const getSkeletonClasses = () => {
    const baseClasses = 'skeleton rounded animate-pulse';
    
    switch (variant) {
      case 'card':
        return `${baseClasses} w-full h-64 rounded-lg`;
      case 'text':
        return `${baseClasses} h-4 rounded`;
      case 'avatar':
        return `${baseClasses} w-12 h-12 rounded-full`;
      case 'button':
        return `${baseClasses} h-10 w-24 rounded-lg`;
      case 'image':
        return `${baseClasses} w-full h-48 rounded-lg`;
      default:
        return baseClasses;
    }
  };

  const skeletonStyle = {
    width: width || undefined,
    height: height || undefined,
  };

  const skeletons = Array.from({ length: count }, (_, index) => (
    <motion.div
      key={index}
      className={`${getSkeletonClasses()} ${className}`}
      style={skeletonStyle}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: index * 0.1 }}
    />
  ));

  if (count === 1) {
    return skeletons[0];
  }

  return (
    <div className="space-y-3">
      {skeletons}
    </div>
  );
};

// Specific skeleton components for common use cases
export const CardSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
    <LoadingSkeleton variant="image" className="mb-4" />
    <LoadingSkeleton variant="text" width="60%" className="mb-2" />
    <LoadingSkeleton variant="text" width="80%" className="mb-2" />
    <LoadingSkeleton variant="text" width="40%" />
  </div>
);

export const TextSkeleton: React.FC<{ lines?: number; className?: string }> = ({ 
  lines = 3, 
  className = '' 
}) => (
  <div className={`space-y-2 ${className}`}>
    {Array.from({ length: lines }, (_, index) => (
      <LoadingSkeleton
        key={index}
        variant="text"
        width={index === lines - 1 ? '60%' : '100%'}
      />
    ))}
  </div>
);

export const AmenitiesSkeleton: React.FC = () => (
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
    {Array.from({ length: 8 }, (_, index) => (
      <CardSkeleton key={index} />
    ))}
  </div>
);

export default LoadingSkeleton;
