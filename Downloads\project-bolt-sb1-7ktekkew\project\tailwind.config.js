/** @type {import('tailwindcss').Config} */
export default {
  content: [ './index.html', './src/**/*.{js,ts,jsx,tsx}' ],
  theme: {
    extend: {
      screens: {
        'xs': '375px',
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
      },
      fontFamily: {
        'heading': [ 'Crimson Text', 'Georgia', 'serif' ],
        'body': [ 'Inter', 'system-ui', '-apple-system', 'sans-serif' ],
        'accent': [ 'Poppins', 'Inter', 'sans-serif' ],
        'sans': [ 'Inter', 'system-ui', '-apple-system', 'sans-serif' ],
        'serif': [ 'Crimson Text', 'Georgia', 'serif' ],
      },
      colors: {
        // Orange Primary Palette (Shreyas Properties Theme)
        primary: {
          50: '#FFF7ED',
          75: '#FFFBF6',  // Custom color between 50 and 100
          100: '#FFEDD5',
          200: '#FED7AA',
          300: '#FDBA74',
          400: '#FB923C',
          500: '#F97316',
          600: '#EA580C',
          700: '#C2410C',
          800: '#9A3412',
          900: '#7C2D12',
          950: '#431407',
          DEFAULT: '#F97316',
        },
        // Complementary Deep Orange/Red Palette
        secondary: {
          50: '#FEF2F2',
          100: '#FEE2E2',
          200: '#FECACA',
          300: '#FCA5A5',
          400: '#F87171',
          500: '#EF4444',
          600: '#DC2626',
          700: '#B91C1C',
          800: '#991B1B',
          900: '#7F1D1D',
          950: '#450A0A',
          DEFAULT: '#DC2626',
        },
        // Warm Neutral Palette
        accent: {
          50: '#FAFAF9',
          100: '#F5F5F4',
          200: '#E7E5E4',
          300: '#D6D3D1',
          400: '#A8A29E',
          500: '#78716C',
          600: '#57534E',
          700: '#44403C',
          800: '#292524',
          900: '#1C1917',
          DEFAULT: '#FAFAF9',
        },
        // Rich Charcoal Palette
        charcoal: {
          50: '#F9FAFB',
          100: '#F3F4F6',
          200: '#E5E7EB',
          300: '#D1D5DB',
          400: '#9CA3AF',
          500: '#6B7280',
          600: '#4B5563',
          700: '#374151',
          800: '#1F2937',
          900: '#111827',
          950: '#030712',
          DEFAULT: '#374151',
        },
        // Luxury Emerald for Success
        success: {
          50: '#ECFDF5',
          100: '#D1FAE5',
          200: '#A7F3D0',
          300: '#6EE7B7',
          400: '#34D399',
          500: '#10B981',
          600: '#059669',
          700: '#047857',
          800: '#065F46',
          900: '#064E3B',
          DEFAULT: '#10B981',
        },
        // Premium Amber for Warnings
        warning: {
          50: '#FFFBEB',
          100: '#FEF3C7',
          200: '#FDE68A',
          300: '#FCD34D',
          400: '#FBBF24',
          500: '#F59E0B',
          600: '#D97706',
          700: '#B45309',
          800: '#92400E',
          900: '#78350F',
          DEFAULT: '#F59E0B',
        },
        // Sophisticated Red for Errors
        error: {
          50: '#FEF2F2',
          100: '#FEE2E2',
          200: '#FECACA',
          300: '#FCA5A5',
          400: '#F87171',
          500: '#EF4444',
          600: '#DC2626',
          700: '#B91C1C',
          800: '#991B1B',
          900: '#7F1D1D',
          DEFAULT: '#EF4444',
        },
      },
      fontSize: {
        'hero': [ '3.5rem', { lineHeight: '1.1', letterSpacing: '-0.02em' } ],
        'hero-mobile': [ '2.25rem', { lineHeight: '1.15', letterSpacing: '-0.01em' } ],
        'section': [ '2.5rem', { lineHeight: '1.2', letterSpacing: '-0.01em' } ],
        'section-mobile': [ '1.875rem', { lineHeight: '1.25', letterSpacing: '-0.005em' } ],
        'heading-lg': [ '2rem', { lineHeight: '1.3', letterSpacing: '-0.005em' } ],
        'heading-md': [ '1.5rem', { lineHeight: '1.35', letterSpacing: '0em' } ],
        'heading-sm': [ '1.25rem', { lineHeight: '1.4', letterSpacing: '0em' } ],
        'body-lg': [ '1.125rem', { lineHeight: '1.7', letterSpacing: '0em' } ],
        'body': [ '1rem', { lineHeight: '1.65', letterSpacing: '0em' } ],
        'body-sm': [ '0.875rem', { lineHeight: '1.6', letterSpacing: '0.01em' } ],
        'caption': [ '0.75rem', { lineHeight: '1.5', letterSpacing: '0.02em' } ],
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'scroll': 'scroll 40s linear infinite',
        'fade-in': 'fadeIn 0.8s ease-out',
        'slide-up': 'slideUp 0.8s ease-out',
        'scale-in': 'scaleIn 0.6s ease-out',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        glow: {
          'from': { boxShadow: '0 0 20px #C5A572' },
          'to': { boxShadow: '0 0 30px #C5A572, 0 0 40px #C5A572' },
        },
        scroll: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-100%)' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(30px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        scaleIn: {
          '0%': { opacity: '0', transform: 'scale(0.9)' },
          '100%': { opacity: '1', transform: 'scale(1)' },
        },
      },
      backgroundImage: {
        // Hero Gradients (Orange Theme)
        'hero-gradient': 'linear-gradient(135deg, rgba(249, 115, 22, 0.95) 0%, rgba(194, 65, 12, 0.85) 50%, rgba(249, 115, 22, 0.9) 100%)',
        'hero-overlay': 'linear-gradient(135deg, rgba(249, 115, 22, 0.8) 0%, rgba(220, 38, 38, 0.1) 100%)',

        // Card Gradients (Orange Theme)
        'card-gradient': 'linear-gradient(135deg, rgba(255, 247, 237, 0.95) 0%, rgba(254, 237, 213, 0.98) 100%)',
        'card-luxury': 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 247, 237, 0.95) 50%, rgba(254, 237, 213, 0.9) 100%)',
        'card-premium': 'linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(249, 115, 22, 0.05) 100%)',

        // Section Gradients (Orange Theme)
        'section-light': 'linear-gradient(180deg, rgba(255, 247, 237, 1) 0%, rgba(254, 237, 213, 1) 100%)',
        'section-dark': 'linear-gradient(180deg, rgba(249, 115, 22, 1) 0%, rgba(194, 65, 12, 1) 100%)',
        'section-accent': 'linear-gradient(135deg, rgba(249, 115, 22, 0.05) 0%, rgba(220, 38, 38, 0.05) 100%)',

        // Glass Effects (Orange Theme)
        'glass': 'linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)',
        'glass-dark': 'linear-gradient(135deg, rgba(249, 115, 22, 0.15) 0%, rgba(194, 65, 12, 0.05) 100%)',
        'glass-orange': 'linear-gradient(135deg, rgba(249, 115, 22, 0.15) 0%, rgba(249, 115, 22, 0.05) 100%)',

        // Button Gradients (Orange Theme)
        'btn-primary': 'linear-gradient(135deg, rgba(249, 115, 22, 1) 0%, rgba(234, 88, 12, 1) 100%)',
        'btn-secondary': 'linear-gradient(135deg, rgba(220, 38, 38, 1) 0%, rgba(185, 28, 28, 1) 100%)',
        'btn-luxury': 'linear-gradient(135deg, rgba(249, 115, 22, 0.9) 0%, rgba(220, 38, 38, 0.1) 50%, rgba(249, 115, 22, 0.95) 100%)',

        // Mesh Gradients for Premium Feel
        'mesh-luxury': 'radial-gradient(circle at 20% 80%, rgba(212, 175, 55, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(30, 41, 59, 0.1) 0%, transparent 50%), radial-gradient(circle at 40% 40%, rgba(250, 250, 249, 0.1) 0%, transparent 50%)',
        'mesh-premium': 'radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.15) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(30, 41, 59, 0.1) 0%, transparent 50%)',
      },
      backdropBlur: {
        'xs': '2px',
      },
      boxShadow: {
        // Luxury Shadows
        'luxury': '0 25px 50px -12px rgba(30, 41, 59, 0.25), 0 0 0 1px rgba(212, 175, 55, 0.05)',
        'luxury-lg': '0 35px 60px -12px rgba(30, 41, 59, 0.3), 0 0 0 1px rgba(212, 175, 55, 0.1)',
        'luxury-xl': '0 45px 80px -15px rgba(30, 41, 59, 0.35), 0 0 0 1px rgba(212, 175, 55, 0.15)',

        // Glass Effects
        'glass': '0 8px 32px 0 rgba(30, 41, 59, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
        'glass-dark': '0 8px 32px 0 rgba(15, 23, 42, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.05)',
        'glass-gold': '0 8px 32px 0 rgba(212, 175, 55, 0.2), inset 0 1px 0 rgba(212, 175, 55, 0.1)',

        // Card Shadows
        'card': '0 4px 6px -1px rgba(30, 41, 59, 0.1), 0 2px 4px -1px rgba(30, 41, 59, 0.06)',
        'card-hover': '0 10px 15px -3px rgba(30, 41, 59, 0.15), 0 4px 6px -2px rgba(30, 41, 59, 0.1)',
        'card-premium': '0 20px 25px -5px rgba(30, 41, 59, 0.15), 0 10px 10px -5px rgba(30, 41, 59, 0.1), 0 0 0 1px rgba(212, 175, 55, 0.05)',

        // Button Shadows
        'btn': '0 4px 14px 0 rgba(30, 41, 59, 0.2)',
        'btn-hover': '0 6px 20px 0 rgba(30, 41, 59, 0.25)',
        'btn-gold': '0 4px 14px 0 rgba(212, 175, 55, 0.3)',
        'btn-gold-hover': '0 6px 20px 0 rgba(212, 175, 55, 0.4)',

        // Inner Shadows for Depth
        'inner': 'inset 0 2px 4px 0 rgba(30, 41, 59, 0.06)',
        'inner-lg': 'inset 0 4px 8px 0 rgba(30, 41, 59, 0.1)',

        // Glow Effects
        'glow': '0 0 20px rgba(212, 175, 55, 0.3)',
        'glow-lg': '0 0 30px rgba(212, 175, 55, 0.4), 0 0 60px rgba(212, 175, 55, 0.2)',
      },
    },
  },
  plugins: [],
};