import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
    Navigation,
    School,
    Landmark,
    Route,
    Plane,
    Building,
    MapPin,
    ExternalLink,
    Car,
    Train,
    HeartPulse,
    ShoppingBag
} from "lucide-react";

// Animation variants for the main container
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.1 },
  },
};

// Animation variants for individual items
const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5,
      ease: "easeOut",
    },
  },
};

// Main Location Component
const Location = () => {
  // Hook to trigger animations when the component scrolls into view
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // State to manage the active tab
  const [activeTab, setActiveTab] = useState("amenities");

  // Data for nearby amenities
  const nearbyAmenities = [
    { icon: Plane, name: "Kempegowda Int'l Airport", distance: "20 min", color: "text-blue-500 bg-blue-100" },
    { icon: Route, name: "NH-44 Highway", distance: "5 min", color: "text-green-500 bg-green-100" },
    { icon: School, name: "Harrow Int'l School", distance: "10 min", color: "text-purple-500 bg-purple-100" },
    { icon: Building, name: "Foxconn iPhone Campus", distance: "15 min", color: "text-slate-500 bg-slate-100" },
    { icon: Landmark, name: "Nandi Hills", distance: "15 min", color: "text-teal-500 bg-teal-100" },
    { icon: School, name: "Amity University", distance: "12 min", color: "text-orange-500 bg-orange-100" },
    { icon: Building, name: "SAP Labs Campus", distance: "18 min", color: "text-sky-500 bg-sky-100" },
    { icon: School, name: "GITAM University", distance: "15 min", color: "text-pink-500 bg-pink-100" },
    { icon: HeartPulse, name: "Devanahalli Hospital", distance: "10 min", color: "text-red-500 bg-red-100" },
  ];

  // Data for transportation links
  const transportation = [
    { icon: Car, name: "Hebbal", time: "25 min" },
    { icon: Plane, name: "Airport", time: "20 min" },
    { icon: Car, name: "Yelahanka", time: "20 min" },
    { icon: Train, name: "Upcoming Metro", time: "8 min" },
  ];

  // Data for neighborhood highlights
   const neighborhoods = [
    { name: "Devanahalli", image: "https://placehold.co/600x400/f97316/ffffff?text=Devanahalli&font=inter", description: "North Bengaluru's fastest-growing investment hotspot.", highlights: ["Airport Proximity", "Industrial Growth", "Educational Hub"] },
    { name: "Hebbal", image: "https://placehold.co/600x400/fb923c/ffffff?text=Hebbal&font=inter", description: "Established locality with excellent infrastructure.", highlights: ["IT Corridor", "Shopping", "Healthcare"] },
    { name: "Yelahanka", image: "https://placehold.co/600x400/fdba74/1c1917?text=Yelahanka&font=inter", description: "Well-developed area with great social infrastructure.", highlights: ["Education", "Parks", "Commercial Hubs"] },
  ];

  const devanahalli = {
    lat: 13.2846,
    lng: 77.6641,
    name: "Shreyas Infra Projects - Devanahalli",
    address: "Devanahalli, North Bengaluru, Karnataka 562110",
  };
  const directionsUrl = `https://www.google.com/maps/dir/?api=1&destination=${devanahalli.lat},${devanahalli.lng}`;
  const placeUrl = `https://www.google.com/maps/place/${devanahalli.address.replace(/\s/g, '+')}`;


  // Function to render the content based on the active tab
  const renderContent = () => {
    switch (activeTab) {
      case "amenities":
        return (
          <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }} className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {nearbyAmenities.map((amenity, index) => (
              <motion.div key={index} variants={itemVariants} className="bg-white rounded-lg p-4 text-center group transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                <div className={`w-12 h-12 rounded-full ${amenity.color} flex items-center justify-center mx-auto mb-3 transition-transform duration-300 group-hover:scale-110`}>
                  <amenity.icon className="h-6 w-6" />
                </div>
                <h4 className="font-semibold text-slate-800 text-sm leading-tight">{amenity.name}</h4>
                <p className="text-orange-600 text-xs font-bold">{amenity.distance}</p>
              </motion.div>
            ))}
          </motion.div>
        );
      case "transport":
        return (
          <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }} className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {transportation.map((transport, index) => (
              <motion.div key={index} variants={itemVariants} className="bg-white rounded-lg p-4 text-center group transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                <div className="w-12 h-12 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center mx-auto mb-3 transition-transform duration-300 group-hover:scale-110">
                  <transport.icon className="h-6 w-6" />
                </div>
                <h4 className="font-semibold text-slate-800 text-sm">{transport.name}</h4>
                <p className="text-slate-500 text-xs">{transport.time}</p>
              </motion.div>
            ))}
          </motion.div>
        );
      case "neighborhoods":
        return (
          <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }} className="grid grid-cols-1 sm:grid-cols-3 gap-6">
            {neighborhoods.map((n, index) => (
              <motion.div key={index} variants={itemVariants} className="bg-white rounded-xl overflow-hidden shadow-md group transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
                <div className="h-32 overflow-hidden">
                  <img src={n.image} alt={n.name} className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500" />
                </div>
                <div className="p-4">
                  <h3 className="font-bold text-slate-800">{n.name}</h3>
                  <p className="text-sm text-slate-600 mt-1 mb-3">{n.description}</p>
                  <div className="flex flex-wrap gap-1.5">
                    {n.highlights.map((h, i) => <span key={i} className="px-2 py-0.5 bg-orange-100 text-orange-800 rounded-full text-xs font-semibold">{h}</span>)}
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        );
      default:
        return null;
    }
  };

  return (
    <section id="location" className="py-20 md:py-10 bg-primary-75 font-sans">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div ref={ref} initial="hidden" animate={inView ? "visible" : "hidden"} variants={containerVariants}>
          {/* Section Header */}
          <div className="text-center mb-16">
            <motion.h2 variants={itemVariants} className="text-4xl md:text-5xl font-bold text-slate-900">Prime Location Advantage</motion.h2>
            <motion.p variants={itemVariants} className="mt-4 text-lg text-slate-600 max-w-3xl mx-auto">Perfectly positioned in North Bangalore's growth corridor, offering unparalleled connectivity and convenience.</motion.p>
            <motion.div variants={itemVariants} className="w-24 h-1.5 bg-orange-500 mx-auto mt-6 rounded-full" />
          </div>

          {/* Main Grid: Map and Details */}
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 lg:gap-12 items-start">
            
            {/* Left Column: Map */}
            <motion.div variants={itemVariants} className="lg:col-span-2 bg-white rounded-2xl shadow-xl p-6 sticky top-8">
                <div className="relative h-64 w-full rounded-xl overflow-hidden group mb-6">
                    <img 
                        src="https://placehold.co/600x400/e2e8f0/334155?text=Location+Map&font=inter"
                        alt="Map showing location of Shreyas Properties in Devanahalli"
                        className="w-full h-full object-cover"
                    />
                     <div className="absolute inset-0 bg-black/20"></div>
                </div>
                <div className="flex items-start space-x-4 mb-6">
                    <div className="flex-shrink-0 w-12 h-12 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center">
                        <MapPin className="w-6 h-6" />
                    </div>
                    <div>
                        <h3 className="font-bold text-lg text-slate-800">Project Location</h3>
                        <p className="text-slate-600">{devanahalli.address}</p>
                    </div>
                </div>
                <div className="flex space-x-3">
                    <a href={directionsUrl} target="_blank" rel="noopener noreferrer" className="flex-1 text-center bg-orange-500 text-white font-semibold py-3 px-4 rounded-lg hover:bg-orange-600 transition-colors duration-300 flex items-center justify-center space-x-2">
                        <Navigation size={18} />
                        <span>Get Directions</span>
                    </a>
                    <a href={placeUrl} target="_blank" rel="noopener noreferrer" className="flex-1 text-center bg-slate-100 text-slate-700 font-semibold py-3 px-4 rounded-lg hover:bg-slate-200 transition-colors duration-300 flex items-center justify-center space-x-2">
                        <ExternalLink size={18} />
                        <span>View on Map</span>
                    </a>
                </div>
            </motion.div>

            {/* Right Column: Details */}
            <motion.div variants={itemVariants} className="lg:col-span-3">
              <div className="flex justify-center md:justify-start mb-6 bg-white rounded-full p-1.5 shadow-md max-w-max">
                {["amenities", "transport", "neighborhoods"].map(tab => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`px-4 py-2 text-sm md:text-base font-semibold rounded-full transition-colors duration-300 capitalize ${
                      activeTab === tab ? "bg-orange-500 text-white" : "text-slate-600 hover:bg-orange-50"
                    }`}
                  >
                    {tab}
                  </button>
                ))}
              </div>
              <div className="min-h-[200px]">
                <AnimatePresence mode="wait">
                  {renderContent()}
                </AnimatePresence>
              </div>
            </motion.div>

          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Location;
