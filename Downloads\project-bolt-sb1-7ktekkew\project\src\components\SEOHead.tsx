import { useEffect } from 'react';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  ogImage?: string;
  canonicalUrl?: string;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title = "Shreyas Sunrise Devanahalli - Premium Plotted Development | Shreyas Properties",
  description = "Discover North Bangalore's most promising investment opportunity - a 30-acre premium gated community strategically located near Kempegowda International Airport and major IT hubs. RERA & DTCP Approved.",
  keywords = "Shreyas Properties, Devanahalli plots, Bangalore real estate, premium plotted development, RERA approved, DTCP approved, investment opportunity, North Bangalore, Kempegowda Airport, gated community",
  ogImage = "/og-image.jpg",
  canonicalUrl = "https://shreyasproperties.com"
}) => {
  useEffect(() => {
    // Update document title
    document.title = title;

    // Update or create meta tags
    const updateMetaTag = (name: string, content: string, property = false) => {
      const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`;
      let meta = document.querySelector(selector) as HTMLMetaElement;
      
      if (!meta) {
        meta = document.createElement('meta');
        if (property) {
          meta.setAttribute('property', name);
        } else {
          meta.setAttribute('name', name);
        }
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', content);
    };

    // Basic SEO meta tags
    updateMetaTag('description', description);
    updateMetaTag('keywords', keywords);
    updateMetaTag('author', 'Shreyas Properties');
    updateMetaTag('robots', 'index, follow');
    updateMetaTag('viewport', 'width=device-width, initial-scale=1.0');

    // Open Graph meta tags
    updateMetaTag('og:title', title, true);
    updateMetaTag('og:description', description, true);
    updateMetaTag('og:image', ogImage, true);
    updateMetaTag('og:url', canonicalUrl, true);
    updateMetaTag('og:type', 'website', true);
    updateMetaTag('og:site_name', 'Shreyas Properties', true);
    updateMetaTag('og:locale', 'en_IN', true);

    // Twitter Card meta tags
    updateMetaTag('twitter:card', 'summary_large_image');
    updateMetaTag('twitter:title', title);
    updateMetaTag('twitter:description', description);
    updateMetaTag('twitter:image', ogImage);

    // Additional SEO meta tags
    updateMetaTag('geo.region', 'IN-KA');
    updateMetaTag('geo.placename', 'Devanahalli, Bangalore, Karnataka');
    updateMetaTag('geo.position', '13.2846;77.7012');
    updateMetaTag('ICBM', '13.2846, 77.7012');

    // Business-specific meta tags
    updateMetaTag('business:contact_data:locality', 'Bangalore');
    updateMetaTag('business:contact_data:region', 'Karnataka');
    updateMetaTag('business:contact_data:country_name', 'India');

    // Update canonical link
    let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
    if (!canonical) {
      canonical = document.createElement('link');
      canonical.setAttribute('rel', 'canonical');
      document.head.appendChild(canonical);
    }
    canonical.setAttribute('href', canonicalUrl);

    // Add structured data (JSON-LD)
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "RealEstateAgent",
      "name": "Shreyas Properties",
      "description": description,
      "url": canonicalUrl,
      "logo": `${canonicalUrl}/logo.png`,
      "image": `${canonicalUrl}${ogImage}`,
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "7-8/1, 4th Main, 4th block, Kalyan Nagar",
        "addressLocality": "Bengaluru",
        "addressRegion": "Karnataka",
        "postalCode": "560043",
        "addressCountry": "IN"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+91-9036699799",
        "contactType": "sales",
        "availableLanguage": ["English", "Hindi", "Kannada"]
      },
      "sameAs": [
        "https://www.facebook.com/ShreyasProperties/",
        "https://www.linkedin.com/company/shreyassunrise/",
        "https://www.youtube.com/watch?v=DaUaY7KRrFI&t=1s"
      ],
      "offers": {
        "@type": "Offer",
        "name": "Shreyas Sunrise Devanahalli",
        "description": "Premium plotted development in Devanahalli",
        "category": "Real Estate",
        "availability": "https://schema.org/InStock"
      }
    };

    let jsonLd = document.querySelector('script[type="application/ld+json"]');
    if (!jsonLd) {
      jsonLd = document.createElement('script');
      jsonLd.setAttribute('type', 'application/ld+json');
      document.head.appendChild(jsonLd);
    }
    jsonLd.textContent = JSON.stringify(structuredData);

  }, [title, description, keywords, ogImage, canonicalUrl]);

  return null; // This component doesn't render anything
};

export default SEOHead;
