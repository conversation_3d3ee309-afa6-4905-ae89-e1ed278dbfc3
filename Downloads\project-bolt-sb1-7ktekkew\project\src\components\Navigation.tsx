import { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { Menu, X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import logoImage from "../assets/logo.png";
import logoWhiteImage from "../assets/logo_white.png";

// --- NAVIGATION COMPONENT ---
const Navigation = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeSection, setActiveSection] = useState("home");

  // Memoize navItems to prevent re-creation on every render
  const navItems = useMemo(() => [
    { name: "Home", href: "#home", id: "home" },
    { name: "About us", href: "#about", id: "about" },
    { name: "Projects", href: "#properties", id: "properties" },
    { name: "Why Invest", href: "#why-invest", id: "why-invest" },
    { name: "Contact us", href: "#contact", id: "contact" },
  ], []);

  // Effect for handling scroll-based UI changes (like the background color)
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Effect for IntersectionObserver-based scrollspy
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveSection(entry.target.id);
          }
        });
      },
      {
        rootMargin: "-20% 0px -70% 0px", // Adjust margin to trigger highlighting at a good spot
        threshold: 0,
      }
    );

    navItems.forEach((item) => {
      const el = document.getElementById(item.id);
      if (el) observer.observe(el);
    });

    return () => observer.disconnect();
  }, [navItems]);


  // Smooth scroll function, wrapped in useCallback for performance
  const scrollToSection = useCallback((event: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    event.preventDefault();
    setIsMobileMenuOpen(false); // Close mobile menu on click

    setTimeout(() => {
      const element = document.querySelector(href);
      if (element) {
        const navHeight = 100; // Height of the nav bar
        const elementPosition = element.getBoundingClientRect().top + window.scrollY;
        const offsetPosition = elementPosition - navHeight;

        window.scrollTo({
          top: offsetPosition,
          behavior: "smooth",
        });
      }
    }, isMobileMenuOpen ? 300 : 0); // Add delay only if closing mobile menu
  }, [isMobileMenuOpen]);

  // --- Dynamic Class Helpers for Readability ---
  const navLinkClasses = (item: {id: string}) => {
    const isActive = activeSection === item.id;
    if (isScrolled) {
      return isActive ? "text-primary-600" : "text-charcoal-700 hover:text-primary-600";
    }
    return isActive ? "text-orange-300" : "text-white hover:text-primary-200 drop-shadow-lg";
  };
  
  const underlineClasses = isScrolled
    ? "from-primary-500 to-primary-600"
    : "from-orange-300 to-orange-400";


  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ y: { duration: 0.6, ease: "easeOut" } }}
      className={`fixed top-0 w-full z-50 transition-all duration-300 ${
        isScrolled
          ? "bg-white/95 backdrop-blur-xl shadow-lg border-b border-primary-200/30"
          : "bg-black/20 backdrop-blur-md shadow-none border-b border-white/10"
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20"> {/* Increased height for better spacing */}
          {/* Logo */}
          <motion.div whileHover={{ scale: 1.05 }} transition={{ type: "spring", stiffness: 300 }}>
            <a href="#home" onClick={(e) => scrollToSection(e, '#home')} className="flex items-center space-x-2">
              <img
                src={activeSection === 'home' && !isScrolled ? logoWhiteImage : logoImage}
                alt="Shreyas Properties"
                className={`h-16 w-auto object-contain transition-all duration-300 ${
                  !isScrolled ? "drop-shadow-[0_2px_8px_rgba(0,0,0,0.8)]" : ""
                }`}
              />
            </a>
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <motion.a
                key={item.name}
                href={item.href}
                onClick={(e) => scrollToSection(e, item.href)}
                className={`px-3 py-2 text-sm font-medium transition-colors duration-300 relative group ${navLinkClasses(item)}`}
                style={{ textShadow: !isScrolled ? '1px 1px 2px rgba(0, 0, 0, 0.7)' : 'none' }}
                whileHover={{ y: -2 }}
                transition={{ type: "spring", stiffness: 300 }}
                aria-current={activeSection === item.id ? "page" : undefined}
              >
                {item.name}
                <motion.div
                  className={`absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r ${underlineClasses}`}
                  animate={{ scaleX: activeSection === item.id ? 1 : 0 }}
                  whileHover={{ scaleX: 1 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                  style={{ originX: 0.5 }}
                />
              </motion.a>
            ))}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <motion.button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className={`p-2 rounded-md transition-colors duration-300 ${
                isScrolled ? "text-charcoal-700 hover:text-primary-600" : "text-white drop-shadow-lg"
              }`}
              whileTap={{ scale: 0.95 }}
              aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
            >
              {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </motion.button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.4, ease: "easeInOut" }}
            className="md:hidden bg-white/98 backdrop-blur-xl border-t border-primary-200/30"
          >
            <div className="px-4 pt-4 pb-6 space-y-2">
              {navItems.map((item, index) => (
                <motion.a
                  key={item.name}
                  href={item.href}
                  onClick={(e) => scrollToSection(e, item.href)}
                  className={`block px-4 py-3 text-base font-medium rounded-lg transition-colors duration-200 ${
                    activeSection === item.id ? "text-primary-600 bg-primary-50" : "text-charcoal-700 hover:bg-primary-50/70"
                  }`}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0, transition: { delay: index * 0.05 } }}
                  aria-current={activeSection === item.id ? "page" : undefined}
                >
                  {item.name}
                </motion.a>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.nav>
  );
};

export default Navigation;