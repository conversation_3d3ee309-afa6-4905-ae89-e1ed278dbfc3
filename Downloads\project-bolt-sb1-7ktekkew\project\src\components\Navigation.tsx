import { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import logoImage from "../assets/logo.png";


// --- NAVIGATION COMPONENT ---
const Navigation = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeSection, setActiveSection] = useState("home");

  const navItems = [
    { name: "Home", href: "#home", id: "home" },
    { name: "About us", href: "#about", id: "about" },
    { name: "Projects", href: "#properties", id: "properties" },
    { name: "Why Invest", href: "#why-invest", id: "why-invest" },
    { name: "Contact us", href: "#contact", id: "contact" },
  ];

  // Scroll detection and scrollspy effect
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setIsScrolled(scrollPosition > 100);

      // Enhanced scrollspy functionality
      const sections = navItems.map(item => document.getElementById(item.id)).filter(Boolean);
      const navHeight = 120;
      const buffer = 100; // Buffer for better detection

      let currentSection = navItems[0].id; // Default to first section

      // Check if we're near the bottom of the page first
      const documentHeight = document.documentElement.scrollHeight;
      const windowHeight = window.innerHeight;
      const isNearBottom = scrollPosition + windowHeight >= documentHeight - 50;

      if (isNearBottom) {
        currentSection = navItems[navItems.length - 1].id; // Set to last section (contact)
      } else {
        // Find the section that's currently most visible
        for (let i = 0; i < sections.length; i++) {
          const section = sections[i];
          if (section) {
            const sectionTop = section.offsetTop - navHeight - buffer;
            const sectionHeight = section.offsetHeight;
            const sectionBottom = sectionTop + sectionHeight + buffer;

            // Check if current scroll position is within this section
            if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
              currentSection = navItems[i].id;
              break;
            }
          }
        }
      }

      setActiveSection(currentSection);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [navItems]);

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      // Close mobile menu first
      setIsMobileMenuOpen(false);

      // Add a small delay to allow menu to close, then scroll
      setTimeout(() => {
        const navHeight = 120; // Approximate height of navigation bar
        const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
        const offsetPosition = elementPosition - navHeight;

        // Try smooth scrolling first
        try {
          window.scrollTo({
            top: offsetPosition,
            behavior: "smooth"
          });
        } catch (error) {
          // Fallback for older browsers or mobile issues
          window.scrollTo(0, offsetPosition);
        }
      }, isMobileMenuOpen ? 300 : 100); // Longer delay if mobile menu is open
    } else {
      // Fallback: just close mobile menu
      setIsMobileMenuOpen(false);
      console.warn(`Navigation target not found: ${href}`);
    }
  };

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{
        y: 0,
        backgroundColor: isScrolled ? "rgba(255, 255, 255, 0.98)" : "rgba(0, 0, 0, 0.2)"
      }}
      transition={{
        y: { duration: 0.6, ease: "easeOut" },
        backgroundColor: { duration: 0.4, ease: "easeInOut" }
      }}
      className={`fixed top-0 w-full z-50 font-sans mobile-safe transition-all duration-400 ${
        isScrolled
          ? "backdrop-blur-xl shadow-luxury border-b border-primary-200/30"
          : "backdrop-blur-md shadow-none border-b border-white/10"
      }`}
      style={{
        backdropFilter: isScrolled ? 'blur(20px) saturate(180%)' : 'blur(10px)'
      }}
    >

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <motion.div
            className="flex-shrink-0"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <a href="#home" className="flex items-center">
              <div className={`relative p-2 rounded-lg transition-all duration-300 ${
                !isScrolled
                  ? "bg-white/90 backdrop-blur-sm shadow-lg"
                  : "bg-transparent"
              }`}>
                <img
                  src={logoImage}
                  alt="Shreyas Properties"
                  className={`h-12 w-auto max-w-[160px] object-contain transition-all duration-300 ${
                    !isScrolled
                      ? "filter-none"
                      : "filter drop-shadow-sm"
                  }`}
                />
              </div>
            </a>
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <motion.button
                key={item.name}
                onClick={() => scrollToSection(item.href)}
                className={`px-3 py-2 text-sm font-medium font-body transition-all duration-300 relative group ${
                  activeSection === item.id
                    ? isScrolled
                      ? "text-primary-600"
                      : "text-orange-300"
                    : isScrolled
                    ? "text-charcoal-700 hover:text-primary-600"
                    : "text-white hover:text-primary-200 drop-shadow-lg"
                }`}
                style={{
                  textShadow: !isScrolled ? '1px 1px 2px rgba(0, 0, 0, 0.7)' : 'none'
                }}
                whileHover={{ y: -1, scale: 1.05 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                {item.name}

                {/* Animated underline */}
                <motion.div
                  className={`absolute bottom-0 left-0 h-0.5 bg-gradient-to-r ${
                    isScrolled
                      ? "from-primary-500 to-primary-600"
                      : "from-orange-300 to-orange-400"
                  }`}
                  initial={{ width: 0 }}
                  animate={{
                    width: activeSection === item.id ? "100%" : 0
                  }}
                  whileHover={{ width: "100%" }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                />

                {/* Hover glow effect */}
                <motion.div
                  className={`absolute inset-0 rounded-md ${
                    isScrolled
                      ? "bg-primary-50/0 group-hover:bg-primary-50/50"
                      : "bg-white/0 group-hover:bg-white/10"
                  }`}
                  transition={{ duration: 0.2 }}
                />
              </motion.button>
            ))}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <motion.button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className={`p-2 rounded-md transition-all duration-300 ${
                isScrolled
                  ? "text-charcoal-700 hover:text-primary-600 hover:bg-primary-50"
                  : "text-white hover:text-primary-200 hover:bg-white/10 drop-shadow-lg"
              }`}
              style={{
                filter: !isScrolled ? 'drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.7))' : 'none'
              }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </motion.button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.4, ease: "easeInOut" }}
            className="md:hidden bg-white/98 backdrop-blur-xl border-t border-primary-200/30 shadow-lg"
            style={{ backdropFilter: 'blur(20px) saturate(180%)' }}
          >
            <div className="px-4 pt-6 pb-8 space-y-1">
              {navItems.map((item, index) => (
                <motion.button
                  key={item.name}
                  onClick={() => {
                    scrollToSection(item.href);
                    setIsMobileMenuOpen(false);
                  }}
                  className={`block px-4 py-4 text-base font-medium font-body w-full text-left rounded-lg transition-all duration-300 relative group ${
                    activeSection === item.id
                      ? "text-primary-600 bg-primary-50 shadow-sm"
                      : "text-charcoal-700 hover:text-primary-600 hover:bg-primary-50/70"
                  }`}
                  initial={{ opacity: 0, x: -30, scale: 0.95 }}
                  animate={{ opacity: 1, x: 0, scale: 1 }}
                  exit={{ opacity: 0, x: -30, scale: 0.95 }}
                  transition={{
                    delay: index * 0.08,
                    type: "spring",
                    stiffness: 400,
                    damping: 25
                  }}
                  whileHover={{ x: 6, scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span className="relative z-10">{item.name}</span>

                  {/* Active indicator */}
                  {activeSection === item.id && (
                    <motion.div
                      className="absolute left-0 top-1/2 w-1 h-8 bg-gradient-to-b from-primary-500 to-primary-600 rounded-r-full"
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 32, opacity: 1 }}
                      transition={{ duration: 0.3, ease: "easeOut" }}
                      style={{ transform: 'translateY(-50%)' }}
                    />
                  )}

                  {/* Hover background effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-primary-500/5 to-primary-600/5 rounded-lg opacity-0 group-hover:opacity-100"
                    transition={{ duration: 0.2 }}
                  />
                </motion.button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.nav>
  );
};

export default Navigation;
