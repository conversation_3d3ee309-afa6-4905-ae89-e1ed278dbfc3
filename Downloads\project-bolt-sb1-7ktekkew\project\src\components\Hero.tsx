import React, { useState, useEffect, useRef } from "react";
import { ArrowDown, TrendingUp } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { gsap } from "gsap";
import { ScrambleTextPlugin } from "gsap/ScrambleTextPlugin";

// Register the GSAP ScrambleTextPlugin
gsap.registerPlugin(ScrambleTextPlugin);

// --- HERO COMPONENT ---
const Hero = () => {
    const [currentImageIndex, setCurrentImageIndex] = useState(0);

    // Refs for GSAP animations
    const subtitleRef = useRef(null);
    const titleRef = useRef(null);
    const descriptionRef = useRef(null);
    const highlightRef = useRef(null);
    const ctaRef = useRef(null);
    const indicatorsRef = useRef(null);
    const scrollIndicatorRef = useRef(null);

    // Hero images for the slideshow
    const heroImages = [
        {
            id: 1,
            src: "https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=2",
            alt: "Premium Gated Development",
        },
        {
            id: 2,
            src: "https://images.pexels.com/photos/1475938/pexels-photo-1475938.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=2",
            alt: "Strategic Location",
        },
        {
            id: 3,
            src: "https://images.pexels.com/photos/2121121/pexels-photo-2121121.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=2",
            alt: "Modern Amenities",
        }
    ];

    // Hero content data
    const heroContent = {
        subtitle: "Premium Plotted Development",
        title: "Shreyas Sunrise Devanahalli",
        description: "Discover North Bangalore's most promising investment opportunity - a 30-acre premium gated community strategically located near Kempegowda International Airport and major IT hubs.",
    };

    // Auto-switch images every 5 seconds
    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentImageIndex((prev) => (prev + 1) % heroImages.length);
        }, 5000);
        return () => clearInterval(timer);
    }, [heroImages.length]);

    // GSAP Animation Timeline
    useEffect(() => {
        const tl = gsap.timeline();

        // Set initial states for standard fade-in-up animation
        gsap.set([subtitleRef.current, descriptionRef.current, highlightRef.current, ctaRef.current], {
            y: 50,
            opacity: 0
        });
        gsap.set([indicatorsRef.current, scrollIndicatorRef.current], {
            y: 20,
            opacity: 0
        });

        // Animate elements with staggered timing
        tl.to(subtitleRef.current, { y: 0, opacity: 1, duration: 0.8, ease: "power2.out" }, 0.8)
          // *** SCRAMBLE TEXT ANIMATION FOR THE TITLE ***
          .to(titleRef.current, {
              duration: 2.5,
              scrambleText: {
                  text: heroContent.title,
                  chars: "ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",
                  revealDelay: 0.5,
                  speed: 0.3,
                  ease: "power2.out"
              }
          }, 1.0)
          .to(descriptionRef.current, { y: 0, opacity: 1, duration: 0.8, ease: "power2.out" }, 1.2)
          .to(highlightRef.current, { y: 0, opacity: 1, scale: 1, duration: 0.8, ease: "power2.out" }, 1.4)
          .to(ctaRef.current, { y: 0, opacity: 1, duration: 0.8, ease: "power2.out" }, 1.6)
          .to(indicatorsRef.current, { y: 0, opacity: 1, duration: 0.6, ease: "power2.out" }, 1.8)
          .to(scrollIndicatorRef.current, { y: 0, opacity: 1, duration: 0.6, ease: "power2.out" }, 2.0);

        // Add floating animation to scroll indicator
        gsap.to(scrollIndicatorRef.current, {
            y: 10,
            duration: 2,
            ease: "power2.inOut",
            repeat: -1,
            yoyo: true,
            delay: 2.5
        });

        return () => {
            tl.kill();
        };
    }, []); // Empty dependency array ensures this runs only once on mount

    const scrollToNext = (id) => {
        document.getElementById(id)?.scrollIntoView({ behavior: "smooth" });
    };

    return (
        <section id="home" className="relative h-screen overflow-hidden font-sans">
            {/* Background Image Slideshow */}
            <div className="absolute inset-0">
                <AnimatePresence>
                    <motion.div
                        key={currentImageIndex}
                        initial={{ opacity: 0, scale: 1.1 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 1.05 }}
                        transition={{ duration: 1.5, ease: "easeInOut" }}
                        className="absolute inset-0 bg-cover bg-center"
                        style={{ backgroundImage: `url(${heroImages[currentImageIndex].src})` }}
                    />
                </AnimatePresence>
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-black/20" />
            </div>

            {/* Content */}
            <div className="relative z-10 flex items-center justify-center min-h-screen px-4 sm:px-6">
                <div className="w-full max-w-5xl mx-auto text-center">
                    <div className="space-y-6 sm:space-y-8">
                        <h2 ref={subtitleRef} className="text-lg md:text-xl font-bold text-orange-300 tracking-[0.2em] uppercase">
                            {/* {heroContent.subtitle} */}
                        </h2>
                        {/* The h1 is now controlled by GSAP, so we just set its initial text */}
                        <h1 ref={titleRef} className="text-4xl sm:text-6xl lg:text-7xl font-black text-white leading-tight tracking-tight drop-shadow-2xl">
                           {/* GSAP will fill this in */}
                        </h1>
                        <p ref={descriptionRef} className="text-base sm:text-lg text-slate-200 max-w-3xl mx-auto">
                            {heroContent.description}
                        </p>
 
                    </div>
                </div>
            </div>

            {/* Image Indicators */}
            <div ref={indicatorsRef} className="absolute bottom-8 left-1/2 -translate-x-1/2 flex space-x-3">
                {heroImages.map((_, index) => (
                    <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`h-2 rounded-full transition-all duration-500 ${
                            index === currentImageIndex ? "w-8 bg-orange-500" : "w-4 bg-white/40 hover:bg-white/60"
                        }`}
                        aria-label={`Go to slide ${index + 1}`}
                    />
                ))}
            </div>

            {/* Scroll Indicator */}
            <button
                ref={scrollIndicatorRef}
                onClick={() => scrollToNext("about")}
                className="absolute bottom-6 right-6 text-white/70 hover:text-orange-300 transition-colors"
                aria-label="Scroll to next section"
            >
                <ArrowDown className="h-6 w-6" />
            </button>
        </section>
    );
};

export default Hero;

