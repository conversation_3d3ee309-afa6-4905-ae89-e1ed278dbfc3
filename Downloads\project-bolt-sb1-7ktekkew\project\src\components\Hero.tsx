import React, { useState, useEffect, useRef } from "react";
import { ArrowDown, TrendingUp, MapPin, Phone } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { gsap } from "gsap";
import { ScrambleTextPlugin } from "gsap/ScrambleTextPlugin";

// Register the GSAP ScrambleTextPlugin
gsap.registerPlugin(ScrambleTextPlugin);

// --- HERO COMPONENT ---
const Hero = () => {
    const [currentImageIndex, setCurrentImageIndex] = useState(0);

    // Refs for GSAP animations
    const subtitleRef = useRef(null);
    const titleRef = useRef(null);
    const descriptionRef = useRef(null);
    const highlightRef = useRef(null);
    const ctaRef = useRef(null);
    const indicatorsRef = useRef(null);
    const scrollIndicatorRef = useRef(null);

    // Hero images for the slideshow
    const heroImages = [
        {
            id: 1,
            src: "https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=2",
            alt: "Premium Gated Development",
        },
        {
            id: 2,
            src: "https://images.pexels.com/photos/1475938/pexels-photo-1475938.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=2",
            alt: "Strategic Location",
        },
        {
            id: 3,
            src: "https://images.pexels.com/photos/2121121/pexels-photo-2121121.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&dpr=2",
            alt: "Modern Amenities",
        }
    ];

    // Hero content data
    const heroContent = {
        subtitle: "Premium Plotted Development",
        title: "Shreyas Sunrise Devanahalli",
        description: "Discover North Bangalore's most promising investment opportunity - a 30-acre premium gated community strategically located near Kempegowda International Airport and major IT hubs.",
    };

    // Auto-switch images every 5 seconds
    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentImageIndex((prev) => (prev + 1) % heroImages.length);
        }, 5000);
        return () => clearInterval(timer);
    }, [heroImages.length]);

    // GSAP Animation Timeline
    useEffect(() => {
        const tl = gsap.timeline();

        // Set initial states for standard fade-in-up animation
        gsap.set([subtitleRef.current, descriptionRef.current, highlightRef.current, ctaRef.current], {
            y: 50,
            opacity: 0
        });
        gsap.set([indicatorsRef.current, scrollIndicatorRef.current], {
            y: 20,
            opacity: 0
        });

        // Animate elements with staggered timing
        tl.to(subtitleRef.current, { y: 0, opacity: 1, duration: 0.8, ease: "power2.out" }, 0.8)
          // *** SCRAMBLE TEXT ANIMATION FOR THE TITLE ***
          .to(titleRef.current, {
              duration: 2.5,
              scrambleText: {
                  text: heroContent.title,
                  chars: "ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",
                  revealDelay: 0.5,
                  speed: 0.3
              },
              ease: "power2.out"
          }, 1.0)
          .to(descriptionRef.current, { y: 0, opacity: 1, duration: 0.8, ease: "power2.out" }, 1.2)
          .to(highlightRef.current, { y: 0, opacity: 1, scale: 1, duration: 0.8, ease: "power2.out" }, 1.4)
          .to(ctaRef.current, { y: 0, opacity: 1, duration: 0.8, ease: "power2.out" }, 1.6)
          .to(indicatorsRef.current, { y: 0, opacity: 1, duration: 0.6, ease: "power2.out" }, 1.8)
          .to(scrollIndicatorRef.current, { y: 0, opacity: 1, duration: 0.6, ease: "power2.out" }, 2.0);

        // Add floating animation to scroll indicator
        gsap.to(scrollIndicatorRef.current, {
            y: 10,
            duration: 2,
            ease: "power2.inOut",
            repeat: -1,
            yoyo: true,
            delay: 2.5
        });

        return () => {
            tl.kill();
        };
    }, []); // Empty dependency array ensures this runs only once on mount

    const scrollToNext = (id) => {
        document.getElementById(id)?.scrollIntoView({ behavior: "smooth" });
    };

    return (
        <section id="home" className="relative h-screen overflow-hidden font-sans">
            {/* Background Image Slideshow */}
            <div className="absolute inset-0">
                <AnimatePresence>
                    <motion.div
                        key={currentImageIndex}
                        initial={{ opacity: 0, scale: 1.1 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 1.05 }}
                        transition={{ duration: 1.5, ease: "easeInOut" }}
                        className="absolute inset-0 bg-cover bg-center"
                        style={{ backgroundImage: `url(${heroImages[currentImageIndex].src})` }}
                    />
                </AnimatePresence>

                {/* Enhanced overlay with vignette and animated gradient */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-black/30" />
                <div className="absolute inset-0 bg-gradient-to-br from-orange-900/20 via-transparent to-orange-800/30" />
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-orange-500/10 to-transparent"
                  animate={{
                    opacity: [0.3, 0.6, 0.3],
                    scale: [1, 1.05, 1]
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />

                {/* Vignette effect */}
                <div className="absolute inset-0" style={{
                  background: 'radial-gradient(ellipse at center, transparent 30%, rgba(0,0,0,0.4) 100%)'
                }} />
            </div>

            {/* Content */}
            <div className="relative z-10 flex items-center justify-center min-h-screen px-4 sm:px-6">
                <div className="w-full max-w-6xl mx-auto text-center">
                    <div className="space-y-8 sm:space-y-10">
                        <motion.h2
                          ref={subtitleRef}
                          className="text-lg md:text-xl font-bold text-orange-300 tracking-[0.2em] uppercase"
                          initial={{ opacity: 0, y: 20 }}
                        >
                            {/* {heroContent.subtitle}   */}
                        </motion.h2>

                        {/* The h1 is now controlled by GSAP, so we just set its initial text */}
                        <h1 ref={titleRef} className="text-4xl sm:text-6xl lg:text-7xl font-black text-white leading-tight tracking-tight drop-shadow-2xl">
                           {/* GSAP will fill this in */}
                        </h1>

                        <p ref={descriptionRef} className="text-lg sm:text-xl text-slate-200 max-w-4xl mx-auto leading-relaxed">
                            {heroContent.description}
                        </p>

                        {/* Highlight Stats */}
                        {/* <motion.div
                          ref={highlightRef}
                          className="flex flex-wrap justify-center gap-6 sm:gap-8 mt-8"
                          initial={{ opacity: 0, y: 30, scale: 0.9 }}
                        >
                          <div className="text-center">
                            <div className="text-2xl sm:text-3xl font-bold text-orange-400">30</div>
                            <div className="text-sm text-slate-300 uppercase tracking-wide">Acres</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl sm:text-3xl font-bold text-orange-400">Premium</div>
                            <div className="text-sm text-slate-300 uppercase tracking-wide">Location</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl sm:text-3xl font-bold text-orange-400">Gated</div>
                            <div className="text-sm text-slate-300 uppercase tracking-wide">Community</div>
                          </div>
                        </motion.div> */}

                        {/* CTA Buttons */}
                        {/* <motion.div
                          ref={ctaRef}
                          className="flex flex-col sm:flex-row gap-4 justify-center items-center mt-10"
                          initial={{ opacity: 0, y: 30 }}
                        >
                          <motion.button
                            onClick={() => document.getElementById('properties')?.scrollIntoView({ behavior: 'smooth' })}
                            className="group relative px-8 py-4 bg-gradient-to-r from-orange-500 to-orange-600 text-white font-semibold rounded-lg shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden"
                            whileHover={{ scale: 1.05, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <span className="relative z-10 flex items-center gap-2">
                              <TrendingUp className="w-5 h-5" />
                              Explore Projects
                            </span>
                            <motion.div
                              className="absolute inset-0 bg-gradient-to-r from-orange-400 to-orange-500"
                              initial={{ x: "-100%" }}
                              whileHover={{ x: 0 }}
                              transition={{ duration: 0.3 }}
                            />
                          </motion.button>

                          <motion.button
                            onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                            className="group relative px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-lg backdrop-blur-sm hover:bg-white/10 transition-all duration-300"
                            whileHover={{ scale: 1.05, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <span className="flex items-center gap-2">
                              <Phone className="w-5 h-5" />
                              Contact Us
                            </span>
                          </motion.button>
                        </motion.div> */}
                    </div>
                </div>
            </div>

            {/* Image Indicators */}
            <div ref={indicatorsRef} className="absolute bottom-8 left-1/2 -translate-x-1/2 flex space-x-3">
                {heroImages.map((_, index) => (
                    <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`h-2 rounded-full transition-all duration-500 ${
                            index === currentImageIndex ? "w-8 bg-orange-500" : "w-4 bg-white/40 hover:bg-white/60"
                        }`}
                        aria-label={`Go to slide ${index + 1}`}
                    />
                ))}
            </div>

            {/* Scroll Indicator */}
            <button
                ref={scrollIndicatorRef}
                onClick={() => scrollToNext("about")}
                className="absolute bottom-6 right-6 text-white/70 hover:text-orange-300 transition-colors"
                aria-label="Scroll to next section"
            >
                <ArrowDown className="h-6 w-6" />
            </button>
        </section>
    );
};

export default Hero;

